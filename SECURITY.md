# Security Policy

## Supported Releases

| Version | Supported          |
| ------- | ------------------ |
| 2.0.1   | :white_check_mark: |
| 2.0.0   | :white_check_mark: |

## Reporting a Vulnerability

Please do not open public github issues or pull requests on this repository for security-related matters. Instead, follow the responsible disclosure process outlined below.

### Scope

This security policy covers the security of this repository and its code. If you discover a security vulnerability related to this repository, please report it responsibly.

### Responsible Disclosure Process

#### Option 1: Reporting via Github Security Advisors

1. **Contact Us**: Submit your report to [https://github.com/idurar/idurar-erp-crm/security](https://github.com/idurar/idurar-erp-crm/security) with details of the vulnerability. Please provide a clear and concise description of the issue, any potential impact, and a step-by-step demonstration if possible. Please do not include sensitive information in your initial email.

2. **Confirmation**: We will acknowledge receipt of your report within [X] business days and provide an estimated timeline for when you can expect a response.

3. **Investigation**: We will investigate the issue, which may involve reproducing the vulnerability or seeking further information from you.

4. **Resolution**: Once the vulnerability is confirmed, we will work to address it promptly and develop a fix.

5. **Disclosure**: We will coordinate with you regarding the public disclosure of the vulnerability. We aim to release a security advisory with information about the issue and the fix.

6. **Credit**: If you report a vulnerability that is successfully fixed, we will credit you for your responsible disclosure in the security advisory unless you prefer to remain anonymous.

#### Option 2: Reporting via Huntr.dev

Alternatively, you can report vulnerabilities through [Huntr.dev](https://huntr.dev). Follow these steps:

1. **Submit Report**: Create a report for this repository on Huntr.dev, providing details of the vulnerability. Include a link to this repository in your report.

2. **Confirmation**: We will be notified of your report on Huntr.dev and will acknowledge it within [X] business days.

3. **Investigation**: We will investigate the issue, which may involve reproducing the vulnerability or seeking further information from you.

4. **Resolution**: Once the vulnerability is confirmed, we will work to address it promptly and develop a fix.

5. **Disclosure**: We will coordinate with you regarding the public disclosure of the vulnerability. We aim to release a security advisory with information about the issue and the fix.

6. **Credit**: If you report a vulnerability that is successfully fixed, we will credit you for your responsible disclosure in the security advisory unless you prefer to remain anonymous.

### Safe Harbor

We consider security research conducted under this policy to be:

- Authorized concerning any applicable anti-hacking laws, and we won't initiate legal action against researchers for their findings.

- Subject to responsible disclosure, where we work with researchers to understand and address reported vulnerabilities before public disclosure.

We appreciate your contributions to the security of this project and community.

Thank you!
