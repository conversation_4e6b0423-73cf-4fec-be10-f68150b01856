# Contributing Guidelines

Welcome to the [idurar-erp-crm](<[idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)>) repository! We're excited that you're interested in contributing. Please take a moment to review this document to ensure a smooth collaboration process.

## How to Contribute

### Reporting Issues

If you find a bug or have a suggestion for an enhancement :

- please make sure it was not asked before here [issues](https://github.com/idurar/idurar-erp-crm/issues).
- make sure it is not work in progress here [pull requests](https://github.com/idurar/idurar-erp-crm/pulls).
- you can then [create an issue](https://github.com/idurar/idurar-erp-crm/issues/new/choose) with the following information:
  - a short but descriptive title.
  - a detailed description of the issue or suggestion (if it is a bug include the steps to reproduce it).

### working on issues

- comment on issues you want to work on and we will assign you to it.
- we do accept pull requests even if you are not assigned to an issue but we prefer you to comment on the issue first.

### Submitting Pull Requests

1. Fork the repository and create your own branch from `dev` branch.
2. follow this format for naming branches:
   - `features/new-profile-page`
   - `issues/fix-authentication-issue`
3. Make sure your code follows our [Coding Guidelines](#coding-guidelines).
4. Commit your changes using clear and descriptive commit messages .
5. Push your changes to your forked repository.
6. Submit a pull request to the `dev` branch of this repository. Please include a detailed description of your changes.

We'll review your pull request as soon as possible. Feedback and suggestions are always welcome.

### Commit Guidelines

Commits should be as small as possible, while ensuring that each commit is
correct independently (i.e., each commit should compile and pass tests).
Please follow this commit format for your commit messages:

- `feat: add new profile page`
- `fix: resolve authentication issue`

### Coding Guidelines

- Use consistent coding style and follow best practices for readability.
- Write clear and concise code comments.
- Don't update dependencies (antd, redux, craco ... ) as it might break the project( we will update all dependencies soon ).
- Do not include changes that are not related to the issue at hand.
- Ensure your code has no conflicts and follows existing patterns.

### Questions

If you have any questions, feel free to reach out by creating an issue.

Thank you for contributing to [idurar-erp-crm](https://github.com/idurar/idurar-erp-crm) dont forget to give us a :star: !
