.hiddenLabel {
  opacity: 0;
  color: #fff;
}
.centerAbsolute {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 32px;
  height: 38px;
  margin-left: -16px;
  margin-top: -19px;
}
.whiteBox {
  background: #fff;
  width: 100%;
  min-height: 100px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.shadow {
  border: 1px solid #e0e0e0;
}
.shadow:hover {
  border: 1px solid #bdbdbd;
}
.line {
  border-top: 1px solid #edf0f5;
  width: 100%;
  margin: 5px auto;
}

.left {
  float: left;
}
.right {
  float: right;
}

.pad5 {
  padding: 5px;
}
.pad10 {
  padding: 10px;
}
.pad15 {
  padding: 15px;
}
.pad20 {
  padding: 20px;
}
.pad25 {
  padding: 25px;
}
.pad30 {
  padding: 30px;
}
.pad33 {
  padding: 35px;
}
.pad40 {
  padding: 40px;
}

.mrg5 {
  margin: 5px;
}
.mrg10 {
  margin: 10px;
}
.mrg15 {
  margin: 15px;
}
.mrg20 {
  margin: 20px;
}
.mrg25 {
  margin: 20px;
}
.mrg30 {
  margin: 20px;
}

.alignRight {
  text-align: right;
}

.alignLeft {
  text-align: left;
}

.center {
  justify-content: center;
}

.strong {
  font-weight: 700;
}

.space5 {
  height: 5px;
  width: 100%;
  display: block;
}
.space10 {
  height: 10px;
  width: 100%;
  display: block;
}
.space20 {
  height: 20px;
  width: 100%;
  display: block;
}
.space30 {
  height: 30px;
  width: 100%;
  display: block;
}
.space40 {
  height: 40px;
  width: 100%;
  display: block;
}
.space50 {
  height: 50px;
  width: 100%;
  display: block;
}
.space60 {
  height: 60px;
  width: 100%;
  display: block;
}
.space70 {
  height: 70px;
  width: 100%;
  display: block;
}

.w-full {
  width: 100%;
}

.circle {
  border-radius: 50%;
}

.capitalize {
  text-transform: capitalize;
}

.layoutPadding {
  position: relative;
  padding: 50px 40px;
}

.dashboardSpacing {
  padding: 30px 40px;
  margin: 70px auto;
}

@media only screen and (min-width: 1100px) {
  .hidden-lg {
    display: none;
  }
}

@media only screen and (min-width: 768px) {
  .hidden-md {
    display: none;
  }
}

@media only screen and (min-width: 480px) {
  .hidden-sm {
    display: none;
  }
}

@media only screen and (max-width: 600px) {
  .layoutPadding {
    padding: 50px 20px;
  }

  .dashboardSpacing {
    padding: 30px 20px;
    margin: 70px auto;
  }
}
