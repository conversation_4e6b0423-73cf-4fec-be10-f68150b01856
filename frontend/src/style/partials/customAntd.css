.ant-layout {
  background: #ffffff !important;
}
.site-layout .site-layout-background {
  background: #fff;
}
[data-theme='dark'] .site-layout .site-layout-background {
  background: #141414;
}

.headerIcon .ant-dropdown-menu {
  border-radius: 6px;
  background: none;
  padding: 0;
  box-shadow: none;
}
.headerIcon .ant-dropdown-menu-item:hover,
.headerIcon .ant-dropdown-menu-item.ant-dropdown-menu-item-active {
  background: none;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

.login-form button[type='submit'] {
  margin-bottom: 20px;
}
.login-form-forgot {
  float: right;
}

.login-form-button {
  width: 100%;
}

.ant-table-thead > tr > th {
  font-weight: 700;
}
.ant-typography strong {
  font-weight: 700;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700 !important;
}

.ant-menu-inline {
  padding-bottom: 70px;
}

.tabContent .ant-tabs-right > .ant-tabs-content-holder,
.tabContent .ant-tabs-right > div > .ant-tabs-content-holder {
  border-right: none;
}

.tabContent .ant-tabs .ant-tabs-tab + .ant-tabs-tab {
  margin: 0;
}

.ant-select-show-search:where(.css-dev-only-do-not-override-2q8sxy).ant-select:not(
    .ant-select-customize-input
  )
  .ant-select-selector {
  cursor: pointer;
}

.ant-menu-item {
  height: 36px !important;
}
@media (max-width: 768px) {
  .tabContent .ant-tabs-right > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-right: 0;
    margin-top: 30px;
  }
  .tabContent .ant-tabs {
    display: block;
  }
}
@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}
