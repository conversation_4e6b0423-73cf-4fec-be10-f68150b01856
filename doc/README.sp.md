#### Spanish Translation


<div align="center">
    <a href="https://www.idurarapp.com/">
  <img src="https://avatars.githubusercontent.com/u/50052356?s=200&v=4" width="128px" />
    </a>
    <h1>ERP/CRM de Código Abierto y "Fair-Code" | Node.js React.js</h1>
    <p align="center">
        <p>IDURAR ERP CRM | Fácil de usar | 44 Idiomas</p>
    </p>
    
  [www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)

## 🇦🇱 🇩🇿 🇧🇩 🇧🇬 🇨🇳 🇭🇷 🇨🇿 🇩🇰 🇳🇱 🇺🇸 🇪🇪 🇫🇷 🇩🇪 🇬🇷 🇮🇳 🇭🇺 🇮🇩 🇮🇹 🇯🇵 🇰🇷 🇱🇻 🇱🇹 🇲🇰 🇲🇾 🇳🇴 🇵🇱 🇧🇷 🇵🇹 🇮🇷 🇷🇴 🇷🇺 🇸🇰 🇸🇮 🇪🇸 🇸🇪 🇹🇭 🇹🇷 🇺🇦 🇵🇰 🇻🇳 🇷🇸 🇪🇦 🇵🇭 🇫🇮

IDURAR es un ERP/CRM de Código Abierto y "Fair-Code" (Facturación/Inventario/Contabilidad/RRHH) basado en Advanced Mern Stack (Node.js/Express.js/MongoDb/React.js) con Ant Design (AntD) y Redux

</div>

**Demo en Vivo** : [https://www.idurarapp.com/demo-erp-crm/](https://www.idurarapp.com/demo-erp-crm/)

**Credenciales** :
```
usuario: <EMAIL>  contraseña: admin123
```

```
🚀 Dale una Estrella ⭐️ y Haz un Fork a este proyecto ...  ¡Feliz codificación!🤩`
```

## Licencia

IDURAR es un Código Abierto Gratuito bajo [fair-code](http://faircode.io) distribuido bajo la
[**IDURAR License 1.0**](https://github.com/idurar/idurar-erp-crm/blob/master/LICENSE)

## Preguntas Frecuentes sobre la Licencia: :

## Características : 

Gestión de Facturas 💰

Gestión de Inventario 🧳

Gestión Contable 📈

Gestión de Recursos Humanos 🧑‍🤝‍🧑

Ant Design Framework(AntD) 🐜

Basado en Mern Stack (Node.js / Express.js / MongoDb / React.js ) 👨‍💻

### ¿Puedo usar IDURAR con fines comerciales? :

- Sí, puedes usar IDURAR de forma gratuita para uso personal o comercial.

### ¿Puedo personalizar IDURAR como SaaS y ofrecerlo a otros usuarios?

No, no puedes personalizar IDURAR como SaaS y ofrecerlo a otros usuarios. No se permite proporcionar el software IDURAR a terceros como un servicio hospedado o gestionado, o como software como servicio (SaaS), donde el servicio proporciona a los usuarios acceso a un conjunto sustancial de funciones o características de este software.


### Para servicios de desarrollo personalizado o soporte premium:

[Ponte en contacto](mailto:<EMAIL>)

## Cómo Desplegar IDURAR ERP CRM :

🔥 Me gustaría invitarte a un seminario web semanal gratuito de IDURAR (curso Node.js React.js), donde aprenderás cómo desplegar IDURAR en la nube y crear una nueva API y una nueva aplicación CRUD con IDURAR en solo una hora.
Completa este formulario si estás interesado: [https://forms.gle/qz2YZ3xQFQ77bGhS8](https://forms.gle/qz2YZ3xQFQ77bGhS8)

El seminario web será este miércoles a la 1 pm GMT.

## Nuestros Patrocinadores

  <a href="https://m.do.co/c/4ead8370b905?ref=idurarapp.com">
    <img src="https://opensource.nyc3.cdn.digitaloceanspaces.com/attribution/assets/PoweredByDO/DO_Powered_by_Badge_blue.svg" width="201px">
  </a>

#

<img width="1403" alt="Open Source ERP CRM" src="https://github.com/idurar/idurar-erp-crm/assets/136928179/a6712286-7ca6-4822-8902-fb7523533ee8">

## Aplicación ERP/CRM de Código Abierto y Gratuito

IDURAR es un ERP/CRM de Código Abierto y "Fair-Code" (Facturación/Inventario/Contabilidad/RRHH) basado en Advanced Mern Stack (Node.js/Express.js/MongoDb/React.js) con Ant Design (AntD) y Redux

**Demo en Vivo** : [https://www.idurarapp.com/demo-erp-crm/](https://www.idurarapp.com/demo-erp-crm/)

## Comenzando


1.[Clona el repositorio](INSTALLATION-INSTRUCTIONS.md#step-1-clone-the-repository)


2.[Crea tu cuenta y clúster de base de datos en MongoDB](INSTALLATION-INSTRUCTIONS.md#Step-2-Create-Your-MongoDB-Account-and-Database-Cluster)

3.[Edita el archivo de entorno](INSTALLATION-INSTRUCTIONS.md#Step-3-Edit-the-Environment-File)

4.[Actualiza la URI de MongoDB](INSTALLATION-INSTRUCTIONS.md#Step-4-Update-MongoDB-URI)

5.[Instala las Dependencias del Backend](INSTALLATION-INSTRUCTIONS.md#Step-5-Install-Backend-Dependencies)

6.[Ejecuta el Script de Configuración](INSTALLATION-INSTRUCTIONS.md#Step-6-Run-Setup-Script)

7.[Ejecuta el Servidor Backend](INSTALLATION-INSTRUCTIONS.md#Step-7-Run-the-Backend-Server)

8.[Instala las Dependencias del Frontend](INSTALLATION-INSTRUCTIONS.md#Step-8-Install-Frontend-Dependencies)

9.[Ejecuta el Servidor Frontend](INSTALLATION-INSTRUCTIONS.md#Step-9-Run-the-Frontend-Server)

## Docker Compose para desarrollo local

- configura variables de entorno adicionales, si es necesario, en el siguiente archivo

```bash
docker-compose.yml
```

- Después de las configuraciones necesarias, ejecuta el siguiente comando:

```bash
docker-compose up -d
```

Esto construirá las imágenes y pondrá en marcha los contenedores para el frontend, backend y MongoDB.

**_NOTA:_**  Esta configuración de docker-compose está asociada solo para desarrollo local.

## Contribuir

1.[Cómo contribuir](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#how-to-contribute)

2.[Reportar problemas](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#reporting-issues)

3.[Trabajar en problemas](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#working-on-issues)

4.[Enviar solicitudes de extracción](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#submitting-pull-requests)

5.[Directrices para los commits](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#commit-guidelines)

6.[Directrices de codificación](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#coding-guidelines)

7.[Preguntas](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#questions)

## Servicio de Desarrollo Personalizado

Los servicios de desarrollo personalizado están disponibles: [Contactez-nous](mailto:<EMAIL>)

## Muestra tu apoyo

¡No olvides darle una ⭐️ a este proyecto ... ¡Feliz codificación!
