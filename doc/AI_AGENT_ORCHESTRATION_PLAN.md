# AI Agent Orchestration Plan
## 5 Specialized HVAC Agents with CrewAI & LangChain Integration

### Executive Summary

Ten dokument przedstawia kompletny plan orkiestracji 5 wyspecjalizowanych agentów AI dla HVAC CRM, wykorzystujących CrewAI framework, LangChain tools i modele Bielik V3/Gemma3-4b. Każdy agent ma określoną rolę w automatyzacji procesów biznesowych HVAC, od obsługi klienta po optymalizację energetyczną.

### Agent Architecture Overview

#### 1. Conversational Agent - Obsługa Klienta

**Rola**: Specjalista ds. komunikacji z klientami w języku polskim
**Model**: Bielik V3 (Polish language specialization)
**Odpowiedzialności**:
- Analiza emaili i transkrypcji rozmów
- Automatyczne odpowiedzi na zapytania klientów
- Klasyfikacja intencji i pilności zgłoszeń
- Generowanie podsumowań rozmów

```python
# agents/conversational_agent.py
from crewai import Agent, Task, Crew
from langchain.tools import Tool
from langchain.llms import Ollama

class ConversationalAgent:
    def __init__(self):
        self.bielik_llm = Ollama(model="bielik-v3", base_url="http://bielik:8877")
        
        self.agent = Agent(
            role="HVAC Customer Service Specialist",
            goal="Zapewnić najwyższą jakość obsługi klienta w branży HVAC",
            backstory="""Jesteś ekspertem w dziedzinie klimatyzacji i wentylacji z 15-letnim 
            doświadczeniem na rynku warszawskim. Specjalizujesz się w komunikacji z klientami, 
            rozumieniu ich potrzeb i dostarczaniu precyzyjnych rozwiązań technicznych.""",
            verbose=True,
            allow_delegation=False,
            llm=self.bielik_llm,
            tools=[
                self._create_email_analyzer_tool(),
                self._create_customer_lookup_tool(),
                self._create_service_scheduler_tool(),
                self._create_response_generator_tool()
            ]
        )
    
    def _create_email_analyzer_tool(self) -> Tool:
        def analyze_email(email_content: str) -> str:
            """Analizuje treść emaila i wyciąga kluczowe informacje"""
            
            analysis_prompt = f"""
            Przeanalizuj poniższy email od klienta HVAC i wyciągnij:
            1. Typ problemu/zapytania
            2. Pilność (1-5)
            3. Wymagane działania
            4. Informacje o urządzeniu (jeśli podane)
            5. Preferowany termin kontaktu
            
            Email: {email_content}
            
            Odpowiedz w formacie JSON.
            """
            
            return self.bielik_llm.invoke(analysis_prompt)
        
        return Tool(
            name="email_analyzer",
            description="Analizuje emaile od klientów HVAC",
            func=analyze_email
        )
    
    def _create_customer_lookup_tool(self) -> Tool:
        def lookup_customer(customer_info: str) -> str:
            """Wyszukuje informacje o kliencie w CRM"""
            # Integration with Svelte CRM API
            import httpx
            
            async def search_customer():
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"http://svelte-crm:3000/api/customers/search",
                        params={"query": customer_info}
                    )
                    return response.json()
            
            return str(search_customer())
        
        return Tool(
            name="customer_lookup",
            description="Wyszukuje dane klienta w systemie CRM",
            func=lookup_customer
        )
    
    async def process_customer_communication(self, communication_data: dict) -> dict:
        """Główna funkcja przetwarzania komunikacji z klientem"""
        
        task = Task(
            description=f"""
            Przeanalizuj komunikację z klientem i wykonaj następujące działania:
            1. Zidentyfikuj klienta i jego historię
            2. Przeanalizuj treść komunikacji
            3. Określ typ problemu i pilność
            4. Zaproponuj odpowiednie działania
            5. Wygeneruj odpowiedź (jeśli potrzebna)
            
            Dane komunikacji: {communication_data}
            """,
            agent=self.agent,
            expected_output="Szczegółowa analiza komunikacji z rekomendowanymi działaniami"
        )
        
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=True
        )
        
        result = crew.kickoff()
        return {"analysis": result, "agent": "conversational"}
```

#### 2. Analytical Agent - Monitoring Danych

**Rola**: Analityk danych i wydajności urządzeń HVAC
**Model**: Gemma3-4b (analytical capabilities)
**Odpowiedzialności**:
- Monitoring wydajności urządzeń
- Analiza trendów zużycia energii
- Wykrywanie anomalii w pracy systemów
- Generowanie raportów analitycznych

```python
# agents/analytical_agent.py
class AnalyticalAgent:
    def __init__(self):
        self.gemma_llm = Ollama(model="gemma3-4b", base_url="http://gemma:8878")
        
        self.agent = Agent(
            role="HVAC Data Analyst & Performance Monitor",
            goal="Monitorować i analizować wydajność systemów HVAC dla optymalizacji działania",
            backstory="""Jesteś data scientist specjalizującym się w analizie danych z systemów 
            HVAC. Masz głęboką wiedzę o wzorcach zużycia energii, cyklach pracy urządzeń i 
            predykcji awarii. Twoje analizy pomagają w optymalizacji kosztów i wydajności.""",
            verbose=True,
            allow_delegation=False,
            llm=self.gemma_llm,
            tools=[
                self._create_performance_monitor_tool(),
                self._create_trend_analyzer_tool(),
                self._create_anomaly_detector_tool(),
                self._create_report_generator_tool()
            ]
        )
    
    def _create_performance_monitor_tool(self) -> Tool:
        def monitor_equipment_performance(equipment_id: str) -> str:
            """Monitoruje wydajność konkretnego urządzenia"""
            
            # Fetch equipment data from GoSpine
            import httpx
            
            async def get_equipment_data():
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"http://gospine:8080/api/equipment/{equipment_id}/performance"
                    )
                    return response.json()
            
            equipment_data = get_equipment_data()
            
            analysis_prompt = f"""
            Przeanalizuj dane wydajności urządzenia HVAC:
            {equipment_data}
            
            Oceń:
            1. Aktualną wydajność (0-100%)
            2. Trendy zużycia energii
            3. Potencjalne problemy
            4. Rekomendacje optymalizacji
            
            Odpowiedz w formacie JSON z konkretnymi metrykami.
            """
            
            return self.gemma_llm.invoke(analysis_prompt)
        
        return Tool(
            name="performance_monitor",
            description="Monitoruje wydajność urządzeń HVAC",
            func=monitor_equipment_performance
        )
    
    async def analyze_system_performance(self, timeframe: str = "7d") -> dict:
        """Analiza wydajności systemu w określonym czasie"""
        
        task = Task(
            description=f"""
            Wykonaj kompleksową analizę wydajności systemów HVAC za ostatnie {timeframe}:
            1. Zbierz dane z wszystkich urządzeń
            2. Przeanalizuj trendy wydajności
            3. Zidentyfikuj anomalie i problemy
            4. Wygeneruj rekomendacje optymalizacji
            5. Stwórz raport z kluczowymi metrykami
            """,
            agent=self.agent,
            expected_output="Szczegółowy raport analityczny z metrykami i rekomendacjami"
        )
        
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=True
        )
        
        result = crew.kickoff()
        return {"analysis": result, "agent": "analytical"}
```

#### 3. Decision Agent - Podejmowanie Decyzji

**Rola**: Agent decyzyjny dla operacji biznesowych
**Model**: Gemma3-4b (decision-making capabilities)
**Odpowiedzialności**:
- Automatyczne podejmowanie decyzji operacyjnych
- Priorytetyzacja zadań serwisowych
- Optymalizacja harmonogramów
- Zarządzanie zasobami

```python
# agents/decision_agent.py
class DecisionAgent:
    def __init__(self):
        self.gemma_llm = Ollama(model="gemma3-4b", base_url="http://gemma:8878")
        
        self.agent = Agent(
            role="HVAC Operations Decision Maker",
            goal="Podejmować optymalne decyzje operacyjne dla maksymalizacji efektywności biznesu HVAC",
            backstory="""Jesteś doświadczonym menedżerem operacyjnym w branży HVAC z 20-letnim 
            doświadczeniem. Specjalizujesz się w optymalizacji procesów, zarządzaniu zasobami 
            i podejmowaniu strategicznych decyzji biznesowych.""",
            verbose=True,
            allow_delegation=True,
            llm=self.gemma_llm,
            tools=[
                self._create_priority_calculator_tool(),
                self._create_resource_optimizer_tool(),
                self._create_schedule_manager_tool(),
                self._create_cost_analyzer_tool()
            ]
        )
    
    def _create_priority_calculator_tool(self) -> Tool:
        def calculate_task_priority(task_data: str) -> str:
            """Kalkuluje priorytet zadania na podstawie wielu czynników"""
            
            priority_prompt = f"""
            Oceń priorytet zadania serwisowego HVAC na podstawie:
            1. Pilność problemu (awaria vs konserwacja)
            2. Wpływ na klienta (VIP, wielkość kontraktu)
            3. Dostępność techników
            4. Koszt opóźnienia
            5. Sezonowość (lato/zima)
            
            Dane zadania: {task_data}
            
            Zwróć priorytet (1-10) z uzasadnieniem.
            """
            
            return self.gemma_llm.invoke(priority_prompt)
        
        return Tool(
            name="priority_calculator",
            description="Kalkuluje priorytet zadań serwisowych",
            func=calculate_task_priority
        )
    
    async def make_operational_decisions(self, decision_context: dict) -> dict:
        """Podejmowanie decyzji operacyjnych"""
        
        task = Task(
            description=f"""
            Na podstawie aktualnej sytuacji operacyjnej podejmij decyzje dotyczące:
            1. Priorytetyzacji zadań serwisowych
            2. Alokacji techników do zadań
            3. Harmonogramowania wizyt
            4. Zarządzania zapasami części
            5. Eskalacji problemów krytycznych
            
            Kontekst decyzyjny: {decision_context}
            """,
            agent=self.agent,
            expected_output="Konkretne decyzje operacyjne z uzasadnieniem"
        )
        
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=True
        )
        
        result = crew.kickoff()
        return {"decisions": result, "agent": "decision"}
```

#### 4. Integration Agent - Automatyzacja Przepływu Danych

**Rola**: Specialist ds. integracji i automatyzacji
**Model**: Bielik V3 (Polish business logic)
**Odpowiedzialności**:
- Synchronizacja danych między systemami
- Automatyzacja workflow'ów biznesowych
- Integracja z systemami zewnętrznymi
- Monitoring przepływu danych

```python
# agents/integration_agent.py
class IntegrationAgent:
    def __init__(self):
        self.bielik_llm = Ollama(model="bielik-v3", base_url="http://bielik:8877")
        
        self.agent = Agent(
            role="HVAC Systems Integration Specialist",
            goal="Zapewnić seamless integrację i automatyzację wszystkich systemów HVAC",
            backstory="""Jesteś ekspertem w dziedzinie integracji systemów IT dla branży HVAC. 
            Specjalizujesz się w automatyzacji procesów biznesowych, synchronizacji danych 
            i tworzeniu efektywnych workflow'ów.""",
            verbose=True,
            allow_delegation=False,
            llm=self.bielik_llm,
            tools=[
                self._create_data_sync_tool(),
                self._create_workflow_automation_tool(),
                self._create_external_integration_tool(),
                self._create_data_validation_tool()
            ]
        )
    
    def _create_data_sync_tool(self) -> Tool:
        def sync_system_data(sync_request: str) -> str:
            """Synchronizuje dane między różnymi systemami"""
            
            sync_prompt = f"""
            Wykonaj synchronizację danych między systemami HVAC:
            {sync_request}
            
            Sprawdź:
            1. Integralność danych
            2. Konflikty i duplikaty
            3. Wymagane transformacje
            4. Status synchronizacji
            
            Zwróć raport synchronizacji.
            """
            
            return self.bielik_llm.invoke(sync_prompt)
        
        return Tool(
            name="data_sync",
            description="Synchronizuje dane między systemami",
            func=sync_system_data
        )
    
    async def automate_business_workflows(self, workflow_data: dict) -> dict:
        """Automatyzacja workflow'ów biznesowych"""
        
        task = Task(
            description=f"""
            Zautomatyzuj następujące procesy biznesowe HVAC:
            1. Przepływ danych między CRM, Python Mixer i Gobeklitepe
            2. Automatyczne tworzenie zleceń serwisowych
            3. Synchronizacja kalendarzy i harmonogramów
            4. Integracja z systemami płatności
            5. Automatyczne generowanie raportów
            
            Dane workflow: {workflow_data}
            """,
            agent=self.agent,
            expected_output="Zautomatyzowane procesy z monitoringiem statusu"
        )
        
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=True
        )
        
        result = crew.kickoff()
        return {"automation": result, "agent": "integration"}
```

#### 5. Optimization Agent - Efektywność Energetyczna

**Rola**: Specialist ds. optymalizacji energetycznej
**Model**: Gemma3-4b (optimization algorithms)
**Odpowiedzialności**:
- Optymalizacja zużycia energii
- Analiza efektywności systemów
- Rekomendacje modernizacji
- Monitoring kosztów operacyjnych

```python
# agents/optimization_agent.py
class OptimizationAgent:
    def __init__(self):
        self.gemma_llm = Ollama(model="gemma3-4b", base_url="http://gemma:8878")
        
        self.agent = Agent(
            role="HVAC Energy Efficiency Optimizer",
            goal="Maksymalizować efektywność energetyczną systemów HVAC przy minimalizacji kosztów",
            backstory="""Jesteś inżynierem energetykiem specjalizującym się w optymalizacji 
            systemów HVAC. Masz głęboką wiedzę o technologiach energooszczędnych, normach 
            efektywności i strategiach redukcji kosztów operacyjnych.""",
            verbose=True,
            allow_delegation=False,
            llm=self.gemma_llm,
            tools=[
                self._create_energy_analyzer_tool(),
                self._create_efficiency_calculator_tool(),
                self._create_optimization_recommender_tool(),
                self._create_cost_optimizer_tool()
            ]
        )
    
    def _create_energy_analyzer_tool(self) -> Tool:
        def analyze_energy_consumption(consumption_data: str) -> str:
            """Analizuje wzorce zużycia energii"""
            
            analysis_prompt = f"""
            Przeanalizuj dane zużycia energii systemu HVAC:
            {consumption_data}
            
            Oceń:
            1. Efektywność energetyczną (kWh/m²)
            2. Wzorce zużycia w czasie
            3. Potencjał oszczędności
            4. Anomalie w zużyciu
            5. Porównanie z benchmarkami branżowymi
            
            Zwróć szczegółową analizę z rekomendacjami.
            """
            
            return self.gemma_llm.invoke(analysis_prompt)
        
        return Tool(
            name="energy_analyzer",
            description="Analizuje zużycie energii systemów HVAC",
            func=analyze_energy_consumption
        )
    
    async def optimize_energy_efficiency(self, system_data: dict) -> dict:
        """Optymalizacja efektywności energetycznej"""
        
        task = Task(
            description=f"""
            Wykonaj kompleksową optymalizację efektywności energetycznej:
            1. Przeanalizuj aktualne zużycie energii
            2. Zidentyfikuj obszary do optymalizacji
            3. Zaproponuj konkretne ulepszenia
            4. Oblicz potencjalne oszczędności
            5. Stwórz plan wdrożenia optymalizacji
            
            Dane systemu: {system_data}
            """,
            agent=self.agent,
            expected_output="Plan optymalizacji z kalkulacją ROI"
        )
        
        crew = Crew(
            agents=[self.agent],
            tasks=[task],
            verbose=True
        )
        
        result = crew.kickoff()
        return {"optimization": result, "agent": "optimization"}
```

### Agent Orchestration System

#### Multi-Agent Coordination

```python
# agents/orchestrator.py
class HVACAgentOrchestrator:
    def __init__(self):
        self.conversational_agent = ConversationalAgent()
        self.analytical_agent = AnalyticalAgent()
        self.decision_agent = DecisionAgent()
        self.integration_agent = IntegrationAgent()
        self.optimization_agent = OptimizationAgent()
        
        self.agent_registry = {
            "conversational": self.conversational_agent,
            "analytical": self.analytical_agent,
            "decision": self.decision_agent,
            "integration": self.integration_agent,
            "optimization": self.optimization_agent
        }
    
    async def process_complex_scenario(self, scenario_data: dict) -> dict:
        """Orkiestracja wielu agentów dla złożonych scenariuszy"""
        
        scenario_type = scenario_data.get("type")
        
        if scenario_type == "customer_issue":
            return await self._handle_customer_issue(scenario_data)
        elif scenario_type == "equipment_maintenance":
            return await self._handle_equipment_maintenance(scenario_data)
        elif scenario_type == "energy_optimization":
            return await self._handle_energy_optimization(scenario_data)
        else:
            return await self._handle_general_scenario(scenario_data)
    
    async def _handle_customer_issue(self, data: dict) -> dict:
        """Obsługa problemu klienta z koordynacją agentów"""
        
        # Step 1: Conversational Agent analyzes communication
        comm_result = await self.conversational_agent.process_customer_communication(data)
        
        # Step 2: Analytical Agent checks equipment performance
        if comm_result.get("equipment_id"):
            analytical_result = await self.analytical_agent.analyze_system_performance("1d")
        else:
            analytical_result = {}
        
        # Step 3: Decision Agent prioritizes and schedules
        decision_context = {
            "communication_analysis": comm_result,
            "equipment_analysis": analytical_result,
            "customer_data": data
        }
        decision_result = await self.decision_agent.make_operational_decisions(decision_context)
        
        # Step 4: Integration Agent automates workflow
        workflow_data = {
            "decisions": decision_result,
            "customer_id": data.get("customer_id"),
            "priority": decision_result.get("priority")
        }
        integration_result = await self.integration_agent.automate_business_workflows(workflow_data)
        
        return {
            "scenario": "customer_issue",
            "communication_analysis": comm_result,
            "equipment_analysis": analytical_result,
            "decisions": decision_result,
            "automation": integration_result,
            "status": "completed"
        }
```

### LangSmith Integration & Monitoring

```python
# monitoring/langsmith_integration.py
from langsmith import Client
from langsmith.run_helpers import traceable

class LangSmithMonitoring:
    def __init__(self):
        self.client = Client()
        
    @traceable(run_type="chain", name="hvac_agent_orchestration")
    async def monitor_agent_performance(self, agent_name: str, task_data: dict, result: dict):
        """Monitor agent performance with LangSmith"""
        
        # Log agent execution
        self.client.create_run(
            name=f"hvac_{agent_name}_execution",
            run_type="llm",
            inputs=task_data,
            outputs=result,
            tags=[f"agent:{agent_name}", "hvac", "production"]
        )
        
        # Calculate performance metrics
        metrics = {
            "response_time": result.get("execution_time", 0),
            "accuracy": self._calculate_accuracy(result),
            "user_satisfaction": result.get("satisfaction_score", 0)
        }
        
        return metrics
    
    def _calculate_accuracy(self, result: dict) -> float:
        """Calculate agent accuracy based on result quality"""
        # Implementation of accuracy calculation
        return 0.95  # Placeholder
```

### Deployment Configuration

```yaml
# docker-compose.agents.yml
version: '3.8'
services:
  agent-orchestrator:
    build: ./agents
    ports:
      - "8083:8083"
    environment:
      - BIELIK_API_URL=http://bielik:8877
      - GEMMA_API_URL=http://gemma:8878
      - SVELTE_CRM_URL=http://svelte-crm:3000
      - GOSPINE_URL=http://gospine:8080
      - PYTHON_MIXER_URL=http://python-mixer:8000
      - GOBEKLITEPE_URL=http://gobeklitepe:8082
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY}
    depends_on:
      - bielik
      - gemma
      - svelte-crm
      - gospine
    networks:
      - hvac-network
```

### Performance Targets

- **Agent Response Time**: < 3s per agent task
- **Multi-Agent Coordination**: < 10s for complex scenarios
- **Accuracy**: 90%+ for decision-making tasks
- **Availability**: 99.9% uptime for agent services
- **Throughput**: 1000+ agent tasks per hour
- **Learning Rate**: Continuous improvement through LangSmith feedback

### Success Metrics

- **Automation Rate**: 80% of routine tasks automated
- **Customer Satisfaction**: 95%+ satisfaction with AI responses
- **Operational Efficiency**: 50% reduction in manual processing
- **Decision Accuracy**: 90%+ accuracy in operational decisions
- **Energy Optimization**: 20% improvement in energy efficiency
- **Cost Reduction**: 30% reduction in operational costs

### Next Steps

1. **Agent Development**: Implement all 5 specialized agents
2. **Integration Testing**: Test agent coordination scenarios
3. **LangSmith Setup**: Configure monitoring and feedback loops
4. **Performance Tuning**: Optimize agent response times
5. **Production Deployment**: Gradual rollout with monitoring
6. **Continuous Learning**: Implement feedback-based improvements
