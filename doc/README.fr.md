#### French Translation


<div align="center">
    <a href="https://www.idurarapp.com/">
  <img src="https://avatars.githubusercontent.com/u/50052356?s=200&v=4" width="128px" />
    </a>
    <h1>Open "Fair-Code" Source ERP / CRM | Node.js React.js</h1>
    <p align="center">
        <p>IDURAR ERP CRM | Simple à utiliser | 44 Langues </p>
    </p>
    
  [www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)

## 🇦🇱 🇩🇿 🇧🇩 🇧🇬 🇨🇳 🇭🇷 🇨🇿 🇩🇰 🇳🇱 🇺🇸 🇪🇪 🇫🇷 🇩🇪 🇬🇷 🇮🇳 🇭🇺 🇮🇩 🇮🇹 🇯🇵 🇰🇷 🇱🇻 🇱🇹 🇲🇰 🇲🇾 🇳🇴 🇵🇱 🇧🇷 🇵🇹 🇮🇷 🇷🇴 🇷🇺 🇸🇰 🇸🇮 🇪🇸 🇸🇪 🇹🇭 🇹🇷 🇺🇦 🇵🇰 🇻🇳 🇷🇸 🇪🇦 🇵🇭 🇫🇮

IDURAR est un Open "Fair-Code" Source ERP / CRM (Facturation / Inventaire / Comptabilité / RH) Bas<PERSON> sue le Mern Stack avancé (Node.js / Express.js / MongoDb / React.js ) avec Ant Design (AntD) et Redux

</div>

**Démo de l'application en direct** : [https://www.idurarapp.com/demo-erp-crm/](https://www.idurarapp.com/demo-erp-crm/)

**Identifiants** :
```
nom d'utilisateur : <EMAIL>  mot de passe : admin123
```

```
🚀 Donnez une étoile ⭐️ & faites un Fork de ce projet ... Bonne programmation! 🤩`
```

## License

IDURAR est un Open Code Source gratuit [fair-code](http://faircode.io) distribué sous la
[**IDURAR License 1.0**](https://github.com/idurar/idurar-erp-crm/blob/master/LICENSE)

## License FAQ :

## Caractéristiques : 

Gestion des factures 💰

Gestion des stocks 🧳

Gestion comptable 📈

Gestion des ressources humaines 🧑‍🤝‍🧑

Ant Design Framework(AntD) 🐜

Basé sur le Mern Stack (Node.js / Express.js / MongoDb / React.js ) 👨‍💻

### Puis-je utiliser IDURAR à des fins commerciales :

- Oui, vous pouvez utiliser IDURAR gratuitement à des fins personnelles ou commerciales.

### Puis-je personnaliser IDURAR en tant que SaaS et le fournir à d'autres utilisateurs ?

Non, vous ne pouvez pas personnaliser IDURAR en tant que SaaS et le fournir à d'autres utilisateurs. Vous n'êtes pas autorisé à fournir le logiciel IDURAR à des tiers en tant que service hébergé ou géré ou en tant que logiciel en tant que service (SaaS), où le service fournit aux utilisateurs un accès à un ensemble substantiel de fonctionnalités de ce logiciel.

### Pour des services de développement personnalisés ou un support premium :

[Contactez-nous](mailto:<EMAIL>)

## Comment déployer IDURAR ERP CRM :

🔥 Je vous invite à un webinaire IDURAR gratuit chaque semaine (cours Node.js React.js), où vous apprendrez à déployer IDURAR sur le cloud et à créer une nouvelle API et une nouvelle application CRUD avec IDURAR en une heure seulement ?
Veuillez remplir ce formulaire si vous êtes intéressé : [https://forms.gle/qz2YZ3xQFQ77bGhS8](https://forms.gle/qz2YZ3xQFQ77bGhS8)

Le webinaire aura lieu ce mercredi à 13h GMT.

## Nos Sponsors

  <a href="https://m.do.co/c/4ead8370b905?ref=idurarapp.com">
    <img src="https://opensource.nyc3.cdn.digitaloceanspaces.com/attribution/assets/PoweredByDO/DO_Powered_by_Badge_blue.svg" width="201px">
  </a>

#

<img width="1403" alt="Open Source ERP CRM" src="https://github.com/idurar/idurar-erp-crm/assets/136928179/a6712286-7ca6-4822-8902-fb7523533ee8">

## Application ERP / CRM Open Source gratuite

IDURAR est un ERP / CRM Open Source basé sur le "Fair-Code" (Facturation / Inventaire / Comptabilité / RH) basé sur la stack Mern (Node.js / Express.js / MongoDb / React.js ) avec Ant Design (AntD) et Redux

**Démo de l'application en direct** : [https://www.idurarapp.com/demo-erp-crm/](https://www.idurarapp.com/demo-erp-crm/)

## Premiers Pas


1.[Cloner le dépôt](INSTALLATION-INSTRUCTIONS.md#step-1-clone-the-repository)


2.[Créer votre compte MongoDB et votre cluster de base de données](INSTALLATION-INSTRUCTIONS.md#Step-2-Create-Your-MongoDB-Account-and-Database-Cluster)

3.[Modifier le fichier d'environnement](INSTALLATION-INSTRUCTIONS.md#Step-3-Edit-the-Environment-File)

4.[Mettre à jour l'URI MongoDB](INSTALLATION-INSTRUCTIONS.md#Step-4-Update-MongoDB-URI)

5.[Installer les dépendances backend](INSTALLATION-INSTRUCTIONS.md#Step-5-Install-Backend-Dependencies)

6.[Exécuter le script de configuration](INSTALLATION-INSTRUCTIONS.md#Step-6-Run-Setup-Script)

7.[Exécuter le serveur backend](INSTALLATION-INSTRUCTIONS.md#Step-7-Run-the-Backend-Server)

8.[Installer les dépendances frontend](INSTALLATION-INSTRUCTIONS.md#Step-8-Install-Frontend-Dependencies)

9.[Exécuter le serveur Frontend](INSTALLATION-INSTRUCTIONS.md#Step-9-Run-the-Frontend-Server)

## Docker Compose pour le développement local

- configurer les variables d'environnement supplémentaires, si nécessaire, dans le fichier ci-dessous


```bash
docker-compose.yml
```

- Après avoir configuré les éléments nécessaires, exécutez la commande suivante :

```bash
docker-compose up -d
```

Cela construira les images et lancera les conteneurs pour le frontend, le backend et MongoDB.

**_Remarque:_** Cette configuration docker-compose est associée uniquement au développement local.

## Contribuer

1.[Comment Contribuer](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#how-to-contribute)

2.[Signaler des problèmes](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#reporting-issues)

3.[Travailler sur des problèmes](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#working-on-issues)

4.[Soumettre des pull requests](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#submitting-pull-requests)

5.[Directives de commit](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#commit-guidelines)

6.[Directives de codage](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#coding-guidelines)

7.[Questions](https://github.com/idurar/idurar-erp-crm/blob/master/CONTRIBUTING.md#questions)

## Service de développement personnalisé

Des services de développement personnalisés sont disponibles : [Contactez-nous](mailto:<EMAIL>)

## Montrez votre soutien

N'oubliez pas de donner une  ⭐️ à ce projet ... Bonne programmation!
