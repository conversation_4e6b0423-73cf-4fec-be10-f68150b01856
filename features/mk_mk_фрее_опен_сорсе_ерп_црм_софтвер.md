# IDURAR Open-Source ERP & CRM Софтвер

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Демо: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Веб-страница: [https://www.idurarapp.com](https://www.idurarapp.com)

## Стек на софтвер

IDURAR Бесплатен отворен извор на erp & crm апликација, базирана на "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Управување со корисници:

- Овозможи на администраторите да креираат, уредуваат и бришат кориснички сметки.
- Имплементирајте контрола на пристапот базирана на улоги за управување со кориснички привилегии.
- Обезбеди механизми за автентикација и авторизација за да се обезбеди безбеден пристап.

## Управување со односите со клиентите (CRM):

- Овозможи на корисниците да креираат и управуваат контактни записи за потенцијални клиенти и клиенти.
- Имплементирајте функционалности за генерирање и квалификација на потенцијални продажбени можност.
- Обезбеди алатки за управување со историјата на комуникација со клиентите, вклучувајќи е-пошти, повици и средби.
- Овозможи на корисниците да закажуваат средби и да испраќаат известувања или потсетници до клиентите.

## Управување со продажбите:

- Дозволи на корисниците да креираат и управуваат нарачки за продажба, поврзувајќи ги со специфични клиенти.
- Имплементирајте следење на залихата за проверка на достапноста на производите и ажурирање на нивните нивоа по секоја продажба.
- Генерирајте фактури и се занимавајте со интеграција на плаќање со популарни платежни портали.
- Обезбеди таблици и извештаи за следење на перформансите на продажбите и анализирање на трендовите.

## Управување со купувањето:

- Дозволи на корисниците да креираат и управуваат нарачки за купување, наведувајќи количина и желени производи.
- Праќајте информации за доставувачот и управувајте со односите со доставувачите.
- Примајте роба и ажурирајте ги нивните нивоа на залиха соодветно.
- Се занимавајте со фактури за купување и плаќање на доставувачите.

## Управување со залихата:

- Обезбеди алатки за управување и следење на нивоата на залиха, вклучувајќи трансфери на залиха и прилагодувања.
- Поставете автоматски известувања за ниски нивоа на залиха и генерирање на нарачки за купување кога е потребно да се дополни залихата.
- Понудете можности за скенирање на баркодови за ефикасно управување со залихата.
- Овозможи на корисниците да категоризираат производи, да дефинираат атрибути и да постават информации за ценување.

## Финансиско управување:

- Имплементирајте систем за главна книга за следење на финансиските трансакции, вклучувајќи трошоци и приходи.
- Управувајте со дебиторски и кредиторски сметки, вклучувајќи фактурирање и следење на плаќањата.
- Генерирајте финансиски извештаи, вклучувајќи балансна состојба и извештаи за приходи и трошоци.
- Интегрирајте го со популарни финансиски софтвери за безпроблемно финансиско управување.

## Управување на проекти:

- Обезбеди можности за управување на проекти, овозможувајќи на корисниците да креираат и следат проекти.
- Доделување на задачи на членовите на тимот, поставување на рокови и следење на напредокот.
- Распределување на ресурси и следење на трошоците на проектот.
- Понудете можности за соработка, како споделување на документи и комуникација во реално време.

## Извештаи и аналитика:

- Генерирајте комплетни извештаи и аналитика за различни аспекти на бизнисот.
- Обезбедете прилагодливи таблици за следење на кључните показатели на перформанси (KPI).
- Дозволете на корисниците да дефинираат прилагодени извештаи базирани на специфични барања.
- Имплементирајте техники за визуелизација на податоците за претставување на информациите на визуелно привлечен начин.

## Интеграција и прилагодување:

- Овозможете интеграција со популарни апликации или API-и од трети страни, како алатки за маркетинг преку е-пошта или CRM платформи.
- Дозволете прилагодување на функционалноста и изгледот на апликацијата врз основа на специфични потреби на бизнисот.
- Обезбедете API или веб-куки за олеснување на размената на податоци помеѓу ERP & CRM апликацијата и други системи.

## Кориснички пријателски интерфејс:

- Дизајнирајте интуитивен, респонзивен и кориснички пријателски интерфејс со користење на React.js и Ant Design.
- Имплементирајте лесно за користење менија за навигација, функционалности за пребарување и филтри.
- Обезбедете конзистентен и визуелно привлечен кориснички интерфејс на различни уреди и големини на екранот.