# IDURAR Open-Source ERP & CRM Software

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Webová stránka: [https://www.idurarapp.com](https://www.idurarapp.com)

## Softvérový stack

IDURAR Bezplatná open-source ERP & CRM aplikácia, založená na "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Správa používateľov:

- Umožňuje administrátorom vytvárať, upravovať a mazať používateľské účty.
- Implementuje riadenie prístupu založené na rolách na správu používateľských oprávnení.
- Poskytuje autentifikačné a autorizačné mechanizmy pre zabezpečený prístup.

## Manažment vzťahov s klientmi (CRM):

- Umožňuje používateľom vytvárať a spravovať kontaktné záznamy pre záujemcov, potenciálnych zákazníkov a zákazníkov.
- Implementuje funkcie generovania a kvalifikácie záujmov na sledovanie potenciálnych predajných príležitostí.
- Poskytuje nástroje na správu histórie komunikácie s klientmi, vrátane e-mailov, hovorov a stretnutí.
- Umožňuje používateľom plánovať stretnutia a posielať oznámenia alebo pripomienky zákazníkom.

## Manažment predaja:

- Umožňuje používateľom vytvárať a spravovať predajné objednávky a priradiť ich konkrétnym zákazníkom.
- Implementuje sledovanie stavu skladu na kontrolu dostupnosti produktov a aktualizáciu úrovní skladu po každom predaji.
- Generuje faktúry a zabezpečuje integráciu platobných brán populárnych platobných systémov.
- Poskytuje prehľady a správy na monitorovanie výkonnosti predaja a analýzu trendov.

## Manažment nákupu:

- Umožňuje používateľom vytvárať a spravovať nákupné objednávky s určením množstva a požadovaných produktov.
- Sleduje informácie o dodávateľoch a spravuje vzťahy s dodávateľmi.
- Prijíma tovar a aktualizuje úrovne skladu.
- Spravuje nákupné faktúry a platby dodávateľom.

## Manažment skladu:

- Poskytuje nástroje na správu a sledovanie úrovní skladu, vrátane presunov a úprav zásob.
- Nastavuje automatické oznámenia o nízkych úrovniach skladu a generuje nákupné objednávky pri potrebe doplnenia zásob.
- Ponúka možnosti skenovania čiarových kódov pre efektívne riadenie skladu.
- Umožňuje používateľom kategorizovať produkty, definovať atribúty a stanoviť cenové informácie.

## Finančný manažment:

- Implementuje systém vedenia hlavnej knihy na sledovanie finančných transakcií, vrátane výdavkov a príjmov.
- Spravuje pohľadávky a záväzky vrátane vystavovania faktúr a sledovania platieb.
- Generuje finančné správy, vrátane bilancií a výkazov ziskov a strát.
- Integruje sa s populárnym účtovným softvérom pre bezproblémový finančný manažment.

## Manažment projektov:

- Poskytuje schopnosti riadenia projektov, umožňujúc používateľom vytvárať a sledovať projekty.
- Priradzuje úlohy členom tímu, stanovuje termíny a monitoruje pokrok.
- Priraďuje zdroje a sleduje náklady na projekty.
- Ponúka funkcie spolupráce, ako je zdieľanie dokumentov a komunikácia v reálnom čase.

## Výkazníctvo a analýza:

- Generuje komplexné správy a analýzy rôznych aspektov podnikania.
- Poskytuje možnosť prispôsobenia prehľadov na monitorovanie kľúčových ukazovateľov výkonnosti (KPI).
- Umožňuje používateľom definovať vlastné správy na základe konkrétnych požiadaviek.
- Implementuje techniky vizualizácie údajov pre prezentáciu informácií v atraktívnej vizuálnej forme.

## Integrácia a prispôsobenie:

- Umožňuje integráciu s populárnymi aplikáciami alebo rozhraniami tretích strán, ako sú nástroje na e-mailový marketing alebo platformy CRM.
- Umožňuje prispôsobenie funkcionality a vzhľadu aplikácie na základe konkrétnych podnikateľských potrieb.
- Poskytuje API alebo webhooks na zjednodušenie výmeny údajov medzi ERP & CRM aplikáciou a inými systémami.

## Používateľsky prívetivé rozhranie:

- Navrhnite intuitívne, responzívne a používateľsky prívetivé rozhranie pomocou React.js a Ant Design.
- Implementuje jednoduché navigačné menu, vyhľadávacie funkcie a filtre.
- Zabezpečuje konzistentné a vizuálne príťažlivé používateľské rozhranie na rôznych zariadeniach a veľkostiach obrazovky.