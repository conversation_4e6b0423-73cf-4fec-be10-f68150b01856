# Perisian Sumber Terbuka IDURAR ERP & CRM

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Laman Web: [https://www.idurarapp.com](https://www.idurarapp.com)

## Tumpukan Perisian

IDURAR adalah aplikasi ERP & CRM sumber terbuka percuma, berdasarkan "tumpukan mern": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Pengurusan Pengguna:

- Membenarkan pentadbir untuk mencipta, mengedit, dan menghapus akaun pengguna.
- Menggunakan kawalan akses berdasarkan peranan untuk menguruskan kebenaran pengguna.
- Menyediakan mekanisme pengesahan dan kebenaran untuk memastikan akses yang selamat.

## Pengurusan Hubungan Pelanggan (CRM):

- Membolehkan pengguna mencipta dan menguruskan rekod hubungan pelanggan untuk prospek, pelanggan potensial, dan pelanggan.
- Mengimplementasikan fungsi penjanaan dan penilaian prospek untuk mengesan peluang jualan yang berpotensi.
- Menyediakan alat untuk menguruskan sejarah komunikasi dengan pelanggan, termasuk emel, panggilan, dan pertemuan.
- Membolehkan pengguna menjadualkan janji temu dan menghantar pemberitahuan atau peringatan kepada pelanggan.

## Pengurusan Jualan:

- Membolehkan pengguna mencipta dan menguruskan pesanan jualan, mengaitkannya dengan pelanggan tertentu.
- Mengimplementasikan penjejakan inventori untuk memeriksa ketersediaan produk dan mengemaskini tahap stok setelah setiap jualan.
- Menghasilkan invois dan menguruskan integrasi pembayaran dengan gerbang pembayaran popular.
- Menyediakan papan pemuka dan laporan untuk memantau prestasi jualan dan menganalisis trend.

## Pengurusan Pembelian:

- Membolehkan pengguna mencipta dan menguruskan pesanan pembelian, menentukan kuantiti dan produk yang dikehendaki.
- Menjejaki maklumat pembekal dan menguruskan hubungan dengan pembekal.
- Menerima barang dan mengemaskini tahap inventori mengikut keadaan.
- Menguruskan invois pembelian dan pembayaran kepada pembekal.

## Pengurusan Inventori:

- Menyediakan alat untuk mengurus dan menjejaki tahap inventori, termasuk pemindahan stok dan penyesuaian.
- Menetapkan pemberitahuan automatik untuk tahap stok yang rendah dan menghasilkan pesanan pembelian apabila perlu mengisi semula stok.
- Menawarkan keupayaan pengimbasan kod bar untuk pengurusan inventori yang cekap.
- Membolehkan pengguna mengategorikan produk, menentukan atribut, dan menetapkan maklumat harga.

## Pengurusan Kewangan:

- Mengimplementasikan sistem buku besar untuk menjejaki transaksi kewangan, termasuk perbelanjaan dan pendapatan.
- Menguruskan piutang dan hutang, termasuk invois dan penjejakan pembayaran.
- Menghasilkan laporan kewangan, termasuk neraca dan penyata pendapatan.
- Mengintegrasikan dengan perisian perakaunan popular untuk pengurusan kewangan yang lancar.

## Pengurusan Projek:

- Menyediakan kemampuan pengurusan projek, membolehkan pengguna mencipta dan menjejaki projek.
- Menetapkan tugas kepada ahli pasukan, menetapkan tarikh akhir, dan memantau kemajuan.
- Mengagihkan sumber dan menjejaki perbelanjaan projek.
- Menawarkan ciri-ciri kolaborasi seperti perkongsian dokumen dan komunikasi secara masa nyata.

## Pelaporan dan Analisis:

- Menghasilkan laporan dan analisis menyeluruh mengenai pelbagai aspek perniagaan.
- Menyediakan papan pemuka yang boleh disesuaikan untuk memantau petunjuk prestasi utama (KPI).
- Membolehkan pengguna menentukan laporan khusus berdasarkan keperluan tertentu.
- Mengimplementasikan teknik visualisasi data untuk menyajikan maklumat secara menarik secara visual.

## Integrasi dan Penyesuaian:

- Membolehkan integrasi dengan aplikasi pihak ketiga atau API popular, seperti alat pemasaran melalui emel atau platform CRM.
- Membolehkan penyesuaian fungsi dan penampilan aplikasi berdasarkan keperluan perniagaan tertentu.
- Menyediakan API atau webhooks untuk memudahkan pertukaran data antara aplikasi ERP & CRM dan sistem lain.

## Antara Muka Mesra Pengguna:

- Reka bentuk antara muka yang intuitif, responsif, dan mesra pengguna menggunakan React.js dan Ant Design.
- Mengimplementasikan menu navigasi yang mudah digunakan, fungsi carian, dan penapis.
- Memastikan antara muka yang konsisten dan menarik secara visual di pelbagai peranti dan saiz skrin.