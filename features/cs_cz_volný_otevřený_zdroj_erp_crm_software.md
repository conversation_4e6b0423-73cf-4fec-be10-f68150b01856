# IDURAR Open-Source ERP & CRM Software

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Webová stránka: [https://www.idurarapp.com](https://www.idurarapp.com)

## Softwarový stack

IDURAR Bezplatná open-source ERP & CRM aplikace, založená na "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Správa uživatelů:

- Umožňuje administrátorům vytvářet, upravovat a mazat uživatelské účty.
- Implementuje řízení přístupu založené na rolích pro správu uživatelských oprávnění.
- Poskytuje mechanismy pro autentizaci a autorizaci, aby byl zajištěn bezpečný přístup.

## Správa vztahů se zákazníky (CRM):

- Umožňuje uživatelům vytvářet a spravovat kontaktní záznamy pro zájemce, potenciální zákazníky a zákazníky.
- Implementuje funkce generování a vyhodnocování příležitostí pro sledování potenciálních prodejních příležitostí.
- Poskytuje nástroje pro správu historie komunikace se zákazníky, včetně e-mailů, hovorů a schůzek.
- Umožňuje uživatelům plánovat schůzky a odesílat oznámení nebo připomínky zákazníkům.

## Správa prodeje:

- Umožňuje uživatelům vytvářet a spravovat prodejní objednávky a přiřazovat je konkrétním zákazníkům.
- Implementuje sledování stavu skladu pro kontrolu dostupnosti produktů a aktualizaci úrovní skladu po každé prodeji.
- Generuje faktury a zajišťuje integraci platebních bran s populárními platebními bránami.
- Poskytuje přehledy a zprávy pro monitorování výkonnosti prodeje a analýzu trendů.

## Správa nákupu:

- Umožňuje uživatelům vytvářet a spravovat nákupní objednávky s určením množství a požadovaných produktů.
- Sleduje informace o dodavatelích a spravuje vztahy s dodavateli.
- Přijímá zboží a aktualizuje úrovně skladu.
- Zajišťuje nákupní faktury a platby dodavatelům.

## Správa skladu:

- Poskytuje nástroje pro správu a sledování úrovní skladu, včetně přesunů a úprav zásob.
- Nastavuje automatická oznámení o nízkých úrovních zásob a generuje nákupní objednávky při potřebě doplnění zásob.
- Nabízí možnost skenování čárových kódů pro efektivní správu skladu.
- Umožňuje uživatelům kategorizovat produkty, definovat atributy a stanovit informace o cenách.

## Finanční řízení:

- Implementuje systém hlavní knihy pro sledování finančních transakcí, včetně výdajů a příjmů.
- Spravuje pohledávky a závazky, včetně fakturace a sledování plateb.
- Generuje finanční zprávy, včetně rozvahy a výkazů zisku a ztráty.
- Integruje se s populárním účetním softwarem pro bezproblémové finanční řízení.

## Řízení projektů:

- Poskytuje možnosti řízení projektů, které umožňují uživatelům vytvářet a sledovat projekty.
- Přiřazuje úkoly členům týmu, stanovuje termíny a monitoruje pokrok.
- Přiděluje zdroje a sleduje náklady na projekty.
- Nabízí funkce spolupráce, jako je sdílení dokumentů a komunikace v reálném čase.

## Reporting a analýza:

- Generuje komplexní zprávy a analýzy různých aspektů podnikání.
- Poskytuje přizpůsobitelné přehledy pro monitorování klíčových ukazatelů výkonnosti (KPI).
- Umožňuje uživatelům definovat vlastní zprávy na základě konkrétních požadavků.
- Implementuje techniky vizualizace dat pro prezentaci informací vizuálně atraktivním způsobem.

## Integrace a přizpůsobení:

- Umožňuje integraci s populárními aplikacemi třetích stran nebo API, jako jsou nástroje pro e-mailový marketing nebo platformy CRM.
- Umožňuje přizpůsobení funkcionality a vzhledu aplikace na základě konkrétních podnikových potřeb.
- Poskytuje API nebo webhooks pro usnadnění výměny dat mezi aplikací ERP & CRM a dalšími systémy.

## Uživatelsky přívětivé rozhraní:

- Navrhněte intuitivní, responzivní a uživatelsky přívětivé rozhraní pomocí React.js a Ant Design.
- Implementuje snadno použitelné navigační menu, vyhledávací funkce a filtry.
- Zajišťuje konzistentní a vizuálně atraktivní uživatelské rozhraní na různých zařízeních a velikostech obrazovky.