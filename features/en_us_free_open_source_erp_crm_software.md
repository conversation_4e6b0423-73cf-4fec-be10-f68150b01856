# IDURAR Open-Source ERP & CRM Software

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Website: [https://www.idurarapp.com](https://www.idurarapp.com)

## Sofware Stack

IDURAR is a free open-source ERP & CRM app based on the "mern-stack". It utilizes the following technologies: Node.js, React.js, Redux, Express.js, MongoDB, and AntDesign (AntD).

## User Management:

- Administrators can create, edit, and delete user accounts.
- Role-based access control is implemented to manage user permissions.
- Authentication and authorization mechanisms ensure secure access.

## Customer Relationship Management (CRM):

- Users can create and manage contact records for leads, prospects, and customers.
- Lead generation and qualification functionalities are implemented to track potential sales opportunities.
- Tools are provided for managing customer communication history, including emails, calls, and meetings.
- Users can schedule appointments and send notifications or reminders to customers.

## Sales Management:

- Users can create and manage sales orders, associating them with specific customers.
- Inventory tracking is implemented to check product availability and update stock levels after each sale.
- Invoices are generated and payment integration with popular payment gateways is handled.
- Dashboards and reports are provided to monitor sales performance and analyze trends.

## Purchase Management:

- Users can create and manage purchase orders, specifying the quantity and desired products.
- Supplier information is tracked and supplier relationships are managed.
- Goods are received and inventory levels are updated accordingly.
- Purchase invoices and payments to suppliers are handled.

## Inventory Management:

- Tools are provided to manage and track inventory levels, including stock transfers and adjustments.
- Automatic notifications for low stock levels are set up, and purchase orders are generated when restocking is required.
- Barcode scanning capabilities are offered for efficient inventory management.
- Users can categorize products, define attributes, and set pricing information.

## Financial Management:

- A general ledger system is implemented to track financial transactions, including expenses and revenue.
- Accounts receivable and accounts payable are managed, including invoicing and payment tracking.
- Financial reports, including balance sheets and income statements, are generated.
- Integration with popular accounting software allows for seamless financial management.

## Project Management:

- Project management capabilities are provided, allowing users to create and track projects.
- Tasks can be assigned to team members, deadlines can be set, and progress can be monitored.
- Resources can be allocated and project expenses can be tracked.
- Collaboration features such as document sharing and real-time communication are offered.

## Reporting and Analytics:

- Comprehensive reports and analytics are generated on various aspects of the business.
- Customizable dashboards are provided to monitor key performance indicators (KPIs).
- Users can define custom reports based on specific requirements.
- Data visualization techniques are implemented to present information in a visually appealing manner.

## Integration and Customization:

- Integration with popular third-party applications or APIs, such as email marketing tools or CRM platforms, is enabled.
- The app's functionality and appearance can be customized based on specific business needs.
- An API or webhooks are provided to facilitate data exchange between the ERP & CRM app and other systems.

## User-friendly Interface:

- An intuitive, responsive, and user-friendly interface is designed using React.js and Ant Design.
- Easy-to-use navigation menus, search functionalities, and filters are implemented.
- A consistent and visually appealing UI is ensured across different devices and screen sizes.