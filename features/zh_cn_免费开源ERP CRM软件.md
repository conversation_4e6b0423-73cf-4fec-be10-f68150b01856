# IDURAR 开源 ERP & CRM 软件

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
演示: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
网站: [https://www.idurarapp.com](https://www.idurarapp.com)

## 软件堆栈

IDURAR 免费开源的 ERP & CRM 应用，基于 "mern-stack"：Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## 用户管理:

- 允许管理员创建、编辑和删除用户账户。
- 实现基于角色的访问控制，管理用户权限。
- 提供身份验证和授权机制，确保安全访问。

## 客户关系管理 (CRM):

- 允许用户创建和管理潜在客户、预期客户和现有客户的联系记录。
- 实现潜在销售机会的生成和资格认定功能，以跟踪潜在销售机会。
- 提供工具来管理客户沟通历史，包括电子邮件、电话和会议。
- 允许用户安排约会并向客户发送通知或提醒。

## 销售管理:

- 允许用户创建和管理销售订单，并将其与特定客户关联。
- 实现库存跟踪，检查产品可用性，并在每次销售后更新库存水平。
- 生成发票，并与流行的支付网关集成处理付款。
- 提供仪表板和报告，监控销售业绩并分析趋势。

## 采购管理:

- 允许用户创建和管理采购订单，指定数量和所需产品。
- 跟踪供应商信息并管理供应商关系。
- 接收货物并相应地更新库存水平。
- 处理采购发票和向供应商付款。

## 库存管理:

- 提供工具来管理和跟踪库存水平，包括库存转移和调整。
- 设置低库存水平的自动通知，并在需要重新进货时生成采购订单。
- 提供条形码扫描功能，实现高效的库存管理。
- 允许用户对产品进行分类、定义属性和设置定价信息。

## 财务管理:

- 实现总账系统，跟踪财务交易，包括支出和收入。
- 管理应收账款和应付账款，包括发票和付款跟踪。
- 生成财务报表，包括资产负债表和利润表。
- 与流行的会计软件集成，实现无缝的财务管理。

## 项目管理:

- 提供项目管理功能，允许用户创建和跟踪项目。
- 分配任务给团队成员，设置截止日期并监控进度。
- 分配资源并跟踪项目费用。
- 提供文档共享和实时通信等协作功能。

## 报告和分析:

- 生成全面的业务报告和分析。
- 提供可自定义的仪表板，监控关键绩效指标 (KPIs)。
- 允许用户根据特定需求定义自定义报告。
- 实施数据可视化技术，以视觉上吸引人的方式呈现信息。

## 集成和定制:

- 支持与流行的第三方应用程序或 API 的集成，例如电子邮件营销工具或 CRM 平台。
- 允许根据特定业务需求定制应用程序的功能和外观。
- 提供 API 或 Webhooks，促进 ERP & CRM 应用程序与其他系统之间的数据交换。

## 用户友好界面:

- 使用 React.js 和 Ant Design 设计直观、响应式和用户友好的界面。
- 实现易于使用的导航菜单、搜索功能和筛选器。
- 确保在不同设备和屏幕尺寸上具有一致且视觉上吸引人的用户界面。