# Perangkat Lunak ERP & CRM Sumber Terbuka IDURAR

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Website: [https://www.idurarapp.com](https://www.idurarapp.com)

## Tumpukan Perangkat Lunak

Aplikasi ERP & CRM gratis dan sumber terbuka IDURAR, berbasis "mern-stack" : Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Manajemen Pengguna:

- Memungkinkan administrator untuk membuat, mengedit, dan menghapus akun pengguna.
- Mengimplementasikan kontrol akses berbasis peran untuk mengelola izin pengguna.
- Menyediakan mekanisme autentikasi dan otorisasi untuk memastikan akses yang aman.

## Manajemen Hubungan Pelanggan (CRM):

- Memungkinkan pengguna untuk membuat dan mengelola catatan kontak untuk prospek dan pelanggan.
- Mengimplementasikan fungsi generasi dan kualifikasi prospek untuk melacak peluang penjualan potensial.
- Menyediakan alat untuk mengelola riwayat komunikasi pelanggan, termasuk email, telepon, dan pertemuan.
- Memungkinkan pengguna untuk menjadwalkan janji dan mengirim pemberitahuan atau pengingat kepada pelanggan.

## Manajemen Penjualan:

- Memungkinkan pengguna untuk membuat dan mengelola pesanan penjualan, yang terkait dengan pelanggan tertentu.
- Mengimplementasikan pelacakan inventaris untuk memeriksa ketersediaan produk dan memperbarui tingkat stok setelah setiap penjualan.
- Menghasilkan faktur dan mengelola integrasi pembayaran dengan gateway pembayaran populer.
- Menyediakan dasbor dan laporan untuk memantau kinerja penjualan dan menganalisis tren.

## Manajemen Pembelian:

- Memungkinkan pengguna untuk membuat dan mengelola pesanan pembelian, dengan menyebutkan jumlah dan produk yang diinginkan.
- Melacak informasi pemasok dan mengelola hubungan dengan pemasok.
- Menerima barang dan memperbarui tingkat persediaan sesuai dengan itu.
- Menangani faktur dan pembayaran pembelian kepada pemasok.

## Manajemen Persediaan:

- Menyediakan alat untuk mengelola dan melacak tingkat persediaan, termasuk transfer stok dan penyesuaian.
- Mengatur notifikasi otomatis untuk tingkat persediaan yang rendah dan menghasilkan pesanan pembelian ketika persediaan perlu diisi ulang.
- Menawarkan kemampuan pemindaian barcode untuk manajemen persediaan yang efisien.
- Memungkinkan pengguna untuk mengategorikan produk, mendefinisikan atribut, dan menetapkan informasi harga.

## Manajemen Keuangan:

- Mengimplementasikan sistem buku besar umum untuk melacak transaksi keuangan, termasuk pengeluaran dan pendapatan.
- Mengelola piutang dan hutang, termasuk faktur dan pelacakan pembayaran.
- Menghasilkan laporan keuangan, termasuk neraca dan laporan laba rugi.
- Mengintegrasikan dengan perangkat lunak akuntansi populer untuk manajemen keuangan yang mulus.

## Manajemen Proyek:

- Menyediakan kemampuan manajemen proyek, memungkinkan pengguna untuk membuat dan melacak proyek.
- Menugaskan tugas kepada anggota tim, menetapkan batas waktu, dan memantau kemajuan.
- Mengalokasikan sumber daya dan melacak biaya proyek.
- Menawarkan fitur kolaborasi seperti berbagi dokumen dan komunikasi real-time.

## Pelaporan dan Analitik:

- Menghasilkan laporan dan analitik komprehensif tentang berbagai aspek bisnis.
- Menyediakan dasbor yang dapat disesuaikan untuk memantau indikator kinerja utama (KPI).
- Memungkinkan pengguna untuk menentukan laporan kustom berdasarkan persyaratan tertentu.
- Mengimplementasikan teknik visualisasi data untuk menyajikan informasi secara menarik secara visual.

## Integrasi dan Kustomisasi:

- Memungkinkan integrasi dengan aplikasi pihak ketiga atau API populer, seperti alat pemasaran email atau platform CRM.
- Memungkinkan kustomisasi fungsionalitas dan tampilan aplikasi berdasarkan kebutuhan bisnis tertentu.
- Menyediakan API atau webhook untuk memfasilitasi pertukaran data antara aplikasi ERP & CRM dan sistem lainnya.

## Antarmuka yang Ramah Pengguna:

- Mendesain antarmuka yang intuitif, responsif, dan ramah pengguna menggunakan React.js dan Ant Design.
- Mengimplementasikan menu navigasi yang mudah digunakan, fungsi pencarian, dan filter.
- Memastikan antarmuka yang konsisten dan menarik secara visual di berbagai perangkat dan ukuran layar.