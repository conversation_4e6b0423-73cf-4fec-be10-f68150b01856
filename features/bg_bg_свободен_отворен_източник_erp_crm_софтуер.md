# IDURAR Софтуер за управление на отворен код ERP & CRM

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Демо: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Уебсайт: [https://www.idurarapp.com](https://www.idurarapp.com)

## Стек от софтуерни технологии

IDURAR е безплатен софтуер с отворен код за управление на ERP & CRM приложения, базиран на "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Управление на потребителите:

- Позволява на администраторите да създават, редактират и изтриват потребителски акаунти.
- Имплементира контрол на достъпа на базата на роли за управление на потребителските права.
- Предоставя механизми за удостоверяване на самоличността и разрешаване на достъпа, за да се осигури сигурен достъп.

## Управление на клиентските взаимоотношения (CRM):

- Позволява на потребителите да създават и управляват контактни записи за потенциални клиенти и клиенти.
- Имплементира функционалности за генериране и квалифициране на потенциални продажби, за проследяване на възможности за продажби.
- Предоставя инструменти за управление на историята на комуникацията с клиентите, включително имейли, обаждания и срещи.
- Позволява на потребителите да планират срещи и да изпращат известия или напомняния на клиентите.

## Управление на продажбите:

- Позволява на потребителите да създават и управляват поръчки за продажби, свързани с конкретни клиенти.
- Имплементира проследяване на наличността на продукти и актуализира нивата на склад след всяка продажба.
- Генерира фактури и се грижи за интеграцията на плащанията с популярни платежни портали.
- Предоставя табла и отчети за наблюдение на продажбите и анализ на тенденциите.

## Управление на закупките:

- Позволява на потребителите да създават и управляват поръчки за закупуване, указвайки количество и желани продукти.
- Проследява информация за доставчици и управлява отношенията с доставчици.
- Получава стоки и актуализира нивата на склада съответно.
- Справя се със закупни фактури и плащания към доставчици.

## Управление на склада:

- Предоставя инструменти за управление и проследяване на нивата на склада, включително прехвърляне на стоки и корекции.
- Задава автоматични известия за ниските нива на наличност и генерира поръчки при необходимост от допълване на запасите.
- Предлага възможности за сканиране на баркодове за ефективно управление на склада.
- Позволява на потребителите да категоризират продуктите, да определят атрибути и да задават информация за ценообразуването.

## Финансово управление:

- Имплементира система за главна книга за проследяване на финансовите транзакции, включително разходи и приходи.
- Управлява дебиторските и кредиторските сметки, включително фактуриране и проследяване на плащанията.
- Генерира финансови отчети, включително балансови отчети и отчети за приходите.
- Интегрира с популярни софтуерни приложения за синхронно финансово управление.

## Управление на проекти:

- Предоставя възможности за управление на проекти, позволяващи на потребителите да създават и проследяват проекти.
- Задава задачи на членове на екипа, срокове и наблюдава напредъка.
- Разпределя ресурси и проследява разходите по проекта.
- Предлага функционалности за сътрудничество, като споделяне на документи и комуникация в реално време.

## Отчети и анализи:

- Генерира изчерпателни отчети и анализи за различни аспекти на бизнеса.
- Предоставя персонализируеми табла за наблюдение на ключови показатели за ефективност (KPI).
- Позволява на потребителите да дефинират персонализирани отчети, базирани на конкретни изисквания.
- Имплементира визуализация на данни за представяне на информация по визуално привлекателен начин.

## Интеграция и персонализация:

- Позволява интеграция с популярни приложения или API-и на трети страни, като инструменти за електронен маркетинг или платформи за управление на клиентски взаимоотношения (CRM).
- Позволява персонализация на функционалността и външния вид на приложението в съответствие с конкретните бизнес нужди.
- Предоставя API или уеб-кукове, за да улесни обмена на данни между ERP & CRM приложението и други системи.

## Потребителски интерфейс, удобен за потребителя:

- Проектира интуитивен, отзивчив и потребителски интерфейс, използвайки React.js и Ant Design.
- Имплементира лесни за използване навигационни менюта, функции за търсене и филтри.
- Осигурява последователен и визуално привлекателен потребителски интерфейс на различни устройства и размери на екрана.