# ซอฟต์แวร์ ERP & CRM โอเพ่นซอร์ส IDURAR

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
เว็บไซต์: [https://www.idurarapp.com](https://www.idurarapp.com)

## สแต็กซอฟต์แวร์

IDURAR เป็นแอปฟรีเปิดตัว ERP & CRM ซึ่งอ้างอิงจาก "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## การจัดการผู้ใช้

- อนุญาตให้ผู้ดูแลระบบสร้าง แก้ไข และลบบัญชีผู้ใช้
- ใช้การควบคุมการเข้าถึงบทบาทเพื่อจัดการสิทธิ์ผู้ใช้
- ให้กลไกการรับรองและอนุญาตเพื่อให้มั่นใจในการเข้าถึงที่ปลอดภัย

## การจัดการความสัมพันธ์กับลูกค้า (CRM)

- อนุญาตให้ผู้ใช้สร้างและจัดการบันทึกการติดต่อสำหรับลีด ลูกค้าเป้าหมาย และลูกค้า
- ดำเนินการสร้างลีดและฟังก์ชันการตรวจสอบคุณสมบัติเพื่อติดตามโอกาสการขายที่เป็นไปได้
- ให้เครื่องมือสำหรับการจัดการประวัติการสื่อสารกับลูกค้า รวมถึงอีเมล โทรศัพท์ และการประชุม
- อนุญาตให้ผู้ใช้กำหนดวันนัดหมายและส่งการแจ้งเตือนหรือการเตือนลูกค้า

## การจัดการการขาย

- อนุญาตให้ผู้ใช้สร้างและจัดการคำสั่งซื้อการขาย โดยผูกมันกับลูกค้าที่เฉพาะเจาะจง
- ดำเนินการติดตามสินค้าในสต็อกเพื่อตรวจสอบสถานะความพร้อมของสินค้าและอัปเดตระดับสต็อกหลังจากทุกครั้งที่มีการขาย
- สร้างใบแจ้งหนี้และจัดการการผสมรวมการชำระเงินกับเกตเวย์การชำระเงินที่นิยม
- ให้แผงควบคุมและรายงานเพื่อตรวจสอบผลงานการขายและวิเคราะห์แนวโน้ม

## การจัดการการซื้อ

- อนุญาตให้ผู้ใช้สร้างและจัดการใบสั่งซื้อการซื้อ โดยระบุปริมาณและผลิตภัณฑ์ที่ต้องการ
- ติดตามข้อมูลผู้ผลิตและจัดการความสัมพันธ์กับผู้ผลิต
- รับสินค้าและอัปเดตระดับสต็อกตามความเหมาะสม
- จัดการใบแจ้งซื้อและการชำระเงินกับผู้ผลิต

## การจัดการสต็อก

- ให้เครื่องมือสำหรับการจัดการและติดตามระดับสต็อก รวมถึงการโอนสต็อกและการปรับปรุง
- ตั้งค่าการแจ้งเตือนอัตโนมัติสำหรับระดับสต็อกต่ำและสร้างใบสั่งซื้อเมื่อต้องการเติมสต็อก
- ให้ความสามารถในการสแกนบาร์โค้ดเพื่อการจัดการสต็อกที่มีประสิทธิภาพ
- อนุญาตให้ผู้ใช้จัดหมวดหมู่ผลิตภัณฑ์ กำหนดคุณสมบัติ และตั้งค่าข้อมูลราคา

## การจัดการทางการเงิน

- ดำเนินการสร้างระบบบัญชีสมุดรายวันเพื่อติดตามธุรกรรมทางการเงิน รวมถึงรายรับและรายจ่าย
- จัดการบัญชีลูกหนี้และบัญชีเจ้าหนี้ รวมถึงการออกใบแจ้งหนี้และการติดตามการชำระเงิน
- สร้างรายงานทางการเงิน รวมถึงงบทดุลและงบกำไรขาดทุน
- ผสานร่วมกับซอฟต์แวร์บัญชีที่นิยมเพื่อการจัดการทางการเงินอย่างราบรื่น

## การจัดการโครงการ

- ให้ความสามารถในการจัดการโครงการ อนุญาตให้ผู้ใช้สร้างและติดตามโครงการ
- กำหนดงานให้สมาชิกในทีม ตั้งกำหนดเวลา และติดตามความคืบหน้า
- จัดสรรทรัพยากรและติดตามค่าใช้จ่ายในโครงการ
- ให้คุณสมบัติการทำงานร่วมกัน เช่นการแบ่งปันเอกสารและการสื่อสารแบบเรียลไทม์

## รายงานและการวิเคราะห์

- สร้างรายงานและการวิเคราะห์ที่ครอบคลุมทั้งหมดในด้านต่างๆ ของธุรกิจ
- ให้แผงควบคุมที่กำหนดเองเพื่อตรวจสอบตัวชี้วัดประสิทธิภาพหลัก (KPI)
- อนุญาตให้ผู้ใช้กำหนดรายงานที่กำหนดเองตามความต้องการเฉพาะ
- ดำเนินการนำเสนอข้อมูลด้วยเทคนิคการแสดงผลที่น่าสนใจตามที่ต้องการ

## การรวมระบบและการปรับแต่ง

- อนุญาตให้รวมระบบกับแอปพลิเคชันหรือ API ที่ได้รับความนิยม เช่นเครื่องมือการตลาดทางอีเมลหรือแพลตฟอร์ม CRM
- อนุญาตให้ปรับแต่งความสามารถและลักษณะของแอปพลิเคชันตามความต้องการธุรกิจเฉพาะ
- ให้ API หรือเว็บฮุกเพื่อสfacilitateการแลกเปลี่ยนข้อมูลระหว่างแอปพลิเคชัน ERP & CRM และระบบอื่นๆ

## อินเตอร์เฟซที่ใช้งานง่าย

- ออกแบบอินเตอร์เฟซที่ใช้งานง่ายและตอบสนองต่อผู้ใช้โดยใช้ React.js และ Ant Design
- ดำเนินการใช้งานเมนูการนำทาง ฟังก์ชันการค้นหา และตัวกรองที่ใช้งานง่าย
- รับประกันการใช้งานอินเตอร์เฟซที่สอดคล้องและมีความสวยงามตรงกับอุปกรณ์และขนาดหน้าจอที่แตกต่างกัน