# IDURAR Atviro kodo ERP ir CRM programinė įranga

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Tinklapis: [https://www.idurarapp.com](https://www.idurarapp.com)

## Programinės įrangos rinkinys

IDURAR Nemokama atviro kodo erp ir crm programa, pagrįsta "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Vartotojų valdymas:

- Leisti administratoriams kurti, redaguoti ir ištrinti vartotojų paskyras.
- Įdiegti vaidmenų pagrįstą prieigos kontrolę, valdyti vartotojų leidimus.
- Užtikrinti autentifikacijos ir autorizacijos mechanizmus, užtikrinančius saugų prieigą.

## K<PERSON><PERSON> ryšių valdymas (CRM):

- Leisti vartotojams kurti ir valdyti kontaktų įrašus potencialiems klientams ir klientams.
- Įdiegti potencialių pardavimų galimybių generavimo ir kvalifikavimo funkcijas.
- Teikti įrankius klientų ryšių istorijos valdymui, įskaitant el. pašto, skambučių ir susitikimų įrašus.
- Leisti vartotojams planuoti susitikimus ir siųsti pranešimus ar priminimus klientams.

## Pardavimų valdymas:

- Leisti vartotojams kurti ir valdyti pardavimų užsakymus, susiejant juos su konkretaus kliento informacija.
- Įdiegti prekių atsekimo sistemą, patikrinti prekių prieinamumą ir atnaujinti atsargas po kiekvieno pardavimo.
- Generuoti sąskaitas faktūras ir tvarkyti mokėjimų integraciją su populiariais mokėjimo šliuzais.
- Teikti informacijos suvestines ir ataskaitas, stebėti pardavimų veiklą ir analizuoti tendencijas.

## Pirkimo valdymas:

- Leisti vartotojams kurti ir valdyti pirkimo užsakymus, nurodydami kiekį ir norimas prekes.
- Sekti tiekimo informaciją ir valdyti tiekimo santykius.
- Gavus prekes, atnaujinti atsargų lygius.
- Tvarkyti pirkimo sąskaitas faktūras ir mokėjimus tiekėjams.

## Atsargų valdymas:

- Suteikti įrankius valdyti ir sekti atsargų lygius, įskaitant atsargų perkėlimus ir korekcijas.
- Nustatyti automatinį pranešimą apie mažas atsargų lygius ir generuoti pirkimo užsakymus, kai reikia papildyti atsargas.
- Siūlyti brūkšninio kodo skenavimo galimybes efektyviam atsargų valdymui.
- Leisti vartotojams kategorizuoti produktus, apibrėžti atributus ir nustatyti kainų informaciją.

## Finansinis valdymas:

- Įdiegti bendrojo žurnalo sistemą finansiniams sandoriams, įskaitant išlaidas ir pajamas.
- Valdyti debitorius ir kreditorius, įskaitant sąskaitų faktūras ir mokėjimų stebėjimą.
- Generuoti finansines ataskaitas, įskaitant balanso ataskaitas ir pelno ir nuostolio ataskaitas.
- Integruoti su populiaria finansinės apskaitos programine įranga, užtikrinant sklandų finansinį valdymą.

## Projektų valdymas:

- Suteikti projektų valdymo galimybes, leidžiančias vartotojams kurti ir stebėti projektus.
- Priskirti užduotis komandos nariams, nustatyti terminus ir stebėti pažangą.
- Skirti išteklius ir stebėti projektų išlaidas.
- Siūlyti bendradarbiavimo funkcijas, tokias kaip dokumentų bendrinimas ir realaus laiko bendravimas.

## Ataskaitos ir analitika:

- Generuoti išsamią ataskaitą ir analitiką apie įvairius verslo aspektus.
- Teikti tinkinamas informacijos suvestines, skirtas stebėti pagrindinius veiklos rodiklius (KPI).
- Leisti vartotojams apibrėžti tinkinamas ataskaitas pagal konkrečius reikalavimus.
- Įdiegti duomenų vizualizavimo technikas, kad informacija būtų pateikiama vizualiai patrauklia forma.

## Integracija ir tinkinimas:

- Leisti integruoti su populiariomis trečiųjų šalių programomis ar API, pvz., el. pašto rinkodaros įrankiais ar CRM platformomis.
- Leisti tinkinti programos funkcionalumą ir išvaizdą pagal konkretų verslo poreikį.
- Teikti API arba webhooks, palengvinančius duomenų mainus tarp ERP ir CRM programos bei kitų sistemų.

## Vartotojui draugiškas sąsaja:

- Sukurti intuityvią, reaguojančią ir vartotojui draugišką sąsają, naudojant React.js ir Ant Design.
- Įdiegti lengvai naudojamus naršymo meniu, paieškos funkcijas ir filtrus.
- Užtikrinti nuoseklią ir vizualiai patrauklią naudotojo sąsają skirtinguose įrenginiuose ir ekrano dydžiuose.