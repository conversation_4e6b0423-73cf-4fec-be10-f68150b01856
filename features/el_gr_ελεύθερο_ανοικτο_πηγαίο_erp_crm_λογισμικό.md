# IDURAR Λογισμικό ERP & CRM ανοιχτού κώδικα

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Επίδειξη: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Ιστοσελίδα: [https://www.idurarapp.com](https://www.idurarapp.com)

## Στοίβα Λογισμικού

Το IDURAR είναι μια δωρεάν εφαρμογή ERP & CRM ανοιχτού κώδικα, βασισμένη στο "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Διαχείριση Χρηστών:

- Επιτρέπει στους διαχειριστές να δημιουργούν, επεξεργάζονται και διαγράφουν λογαριασμούς χρηστών.
- Υλοποιεί έλεγχο πρόσβασης με βάση τον ρόλο για τη διαχείριση των δικαιωμάτων των χρηστών.
- Παρέχει μηχανισμούς πιστοποίησης και εξουσιοδότησης για να εξασφαλίσει ασφαλή πρόσβαση.

## Διαχείριση Σχέσεων με Πελάτες (CRM):

- Επιτρέπει στους χρήστες να δημιουργούν και να διαχειρίζονται επαφές για προοπτικούς πελάτες και πελάτες.
- Υλοποιεί λειτουργίες για την παραγωγή και την πιστοποίηση προοπτικών πωλήσεων για την παρακολούθηση δυνατοτήτων πωλήσεων.
- Παρέχει εργαλεία για τη διαχείριση του ιστορικού επικοινωνίας με τους πελάτες, συμπεριλαμβανομένων των ηλεκτρονικών μηνυμάτων, των τηλεφωνικών κλήσεων και των συναντήσεων.
- Επιτρέπει στους χρήστες να προγραμματίζουν ραντεβού και να στέλνουν ειδοποιήσεις ή υπενθυμίσεις στους πελάτες.

## Διαχείριση Πωλήσεων:

- Επιτρέπει στους χρήστες να δημιουργούν και να διαχειρίζονται παραγγελίες πωλήσεων, συσχετίζοντάς τις με συγκεκριμένους πελάτες.
- Υλοποιεί παρακολούθηση αποθέματος για να ελέγχει τη διαθεσιμότητα των προϊόντων και να ενημερώνει τα επίπεδα αποθέματος μετά από κάθε πώληση.
- Δημιουργεί τιμολόγια και χειρίζεται την ολοκλήρωση πληρωμής με δημοφιλείς πύλες πληρωμής.
- Παρέχει πίνακες ελέγχου και αναφορές για την παρακολούθηση της απόδοσης των πωλήσεων και την ανάλυση των τάσεων.

## Διαχείριση Αγορών:

- Επιτρέπει στους χρήστες να δημιουργούν και να διαχειρίζονται παραγγελίες αγορών, καθορίζοντας την ποσότητα και τα επιθυμητά προϊόντα.
- Παρακολουθεί τις πληροφορίες των προμηθευτών και διαχειρίζεται τις σχέσεις με τους προμηθευτές.
- Λαμβάνει αγαθά και ενημερώνει τα επίπεδα αποθέματος αναλόγως.
- Χειρίζεται τις αγοραπωλησίες και τις πληρωμές προς τους προμηθευτές.

## Διαχείριση Αποθέματος:

- Παρέχει εργαλεία για τη διαχείριση και την παρακολούθηση των επιπέδων αποθέματος, συμπεριλαμβανομένων των μεταφορών αποθέματος και των προσαρμογών.
- Ρυθμίζει αυτόματες ειδοποιήσεις για χαμηλά επίπεδα αποθέματος και δημιουργεί παραγγελίες αγοράς όταν απαιτείται ανεφοδιασμός.
- Παρέχει δυνατότητες σάρωσης γραφικών κωδικών για αποτελεσματική διαχείριση αποθέματος.
- Επιτρέπει στους χρήστες να κατηγοριοποιούν προϊόντα, να καθορίζουν χαρακτηριστικά και να ορίζουν πληροφορίες τιμολόγησης.

## Οικονομική Διαχείριση:

- Υλοποιεί ένα σύστημα γενικού λογιστηρίου για την καταγραφή οικονομικών συναλλαγών, συμπεριλαμβανομένων των εξόδων και των εσόδων.
- Διαχειρίζεται τους λογαριασμούς πιστωτών και χρεωστών, συμπεριλαμβανομένης της τιμολόγησης και της παρακολούθησης πληρωμών.
- Δημιουργεί οικονομικές αναφορές, συμπεριλαμβανομένων των ισολογισμών και των καταστάσεων αποτελεσμάτων.
- Ενσωματώνεται με δημοφιλές λογισμικό λογιστικής για απροβλημάτιστη οικονομική διαχείριση.

## Διαχείριση Έργων:

- Παρέχει δυνατότητες διαχείρισης έργων, επιτρέποντας στους χρήστες να δημιουργούν και να παρακολουθούν έργα.
- Αναθέτει εργασίες σε μέλη της ομάδας, ορίζει προθεσμίες και παρακολουθεί την πρόοδο.
- Κατανέμει πόρους και παρακολουθεί τα έξοδα του έργου.
- Προσφέρει δυνατότητες συνεργασίας, όπως κοινή χρήση εγγράφων και επικοινωνία σε πραγματικό χρόνο.

## Αναφορές και Αναλύσεις:

- Δημιουργεί λεπτομερείς αναφορές και αναλύσεις για διάφορες πτυχές της επιχείρησης.
- Παρέχει προσαρμόσιμους πίνακες ελέγχου για την παρακολούθηση των βασικών δεικτών απόδοσης (KPIs).
- Επιτρέπει στους χρήστες να ορίζουν προσαρμοσμένες αναφορές βάσει συγκεκριμένων απαιτήσεων.
- Υλοποιεί τεχνικές οπτικοποίησης δεδομένων για να παρουσιάσει τις πληροφορίες με ελκυστικό τρόπο.

## Ενσωμάτωση και Προσαρμογή:

- Επιτρέπει την ενσωμάτωση με δημοφιλείς εφαρμογές τρίτων ή διεπαφές προγραμματισμού εφαρμογών (APIs), όπως εργαλεία email marketing ή πλατφόρμες CRM.
- Επιτρέπει την προσαρμογή της λειτουργικότητας και της εμφάνισης της εφαρμογής βάσει των συγκεκριμένων αναγκών της επιχείρησης.
- Παρέχει ένα API ή webhooks για να διευκολύνει την ανταλλαγή δεδομένων μεταξύ της εφαρμογής ERP & CRM και άλλων συστημάτων.

## Χρήστική Διεπαφή:

- Σχεδιάζει μια ευανάγνωστη, ευέλικτη και χρήστική διεπαφή χρήστη χρησιμοποιώντας το React.js και το Ant Design.
- Υλοποιεί εύχρηστα μενού πλοήγησης, λειτουργίες αναζήτησης και φίλτρων.
- Βεβαιώνεται για μια συνεπή και αισθητικά ελκυστική διεπαφή χρήστη σε διάφορες συσκευές και μεγέθη οθονών.