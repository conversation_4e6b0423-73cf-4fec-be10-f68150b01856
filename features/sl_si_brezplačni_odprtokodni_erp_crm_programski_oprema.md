# IDURAR Odprtokodna ERP in CRM programska oprema

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Spletna stran: [https://www.idurarapp.com](https://www.idurarapp.com)

## Sklad programske opreme

IDURAR Brezplačna odprtokodna ERP in CRM aplikacija, zasnovana na "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Upravljanje uporabnikov:

- Omogočanje administratorjem ustvarjanje, urejanje in brisanje uporabniških računov.
- Uvedba upravljanja dostopa na podlagi vlog za upravljanje uporabniških dovoljenj.
- Zagotavljanje mehanizmov za preverjanje pristnosti in pooblastil za zagotavljanje varnega dostopa.

## Upravljanje odnosov s strankami (CRM):

- Omogočanje uporabnikom ustvarjanje in upravljanje stikov za potencialne stranke in stranke.
- Uvedba funkcionalnosti za generiranje in kvalifikacijo potencialnih prodajnih priložnosti.
- Zagotavljanje orodij za upravljanje zgodovine komunikacije s strankami, vključno z elektronsko pošto, klici in sestanki.
- Omogočanje uporabnikom, da načrtujejo sestanke in pošiljajo obvestila ali opomnike strankam.

## Upravljanje prodaje:

- Omogočanje uporabnikom ustvarjanje in upravljanje prodajnih naročil, povezovanje z določenimi strankami.
- Uvedba sledenja zalogam za preverjanje razpoložljivosti izdelkov in posodabljanje stanja zalog po vsaki prodaji.
- Generiranje računov in obvladovanje integracije plačil z priljubljenimi plačilnimi prehodi.
- Zagotavljanje nadzornih plošč in poročil za spremljanje prodajne uspešnosti in analizo trendov.

## Upravljanje naročilnic:

- Omogočanje uporabnikom ustvarjanje in upravljanje naročilnic, določanje količine in želenih izdelkov.
- Sledenje informacijam o dobaviteljih in upravljanje odnosov z dobavitelji.
- Prejemanje blaga in ustrezno posodabljanje stanja zalog.
- Obvladovanje naročilnic in plačil dobaviteljem.

## Upravljanje zalog:

- Zagotavljanje orodij za upravljanje in sledenje stanju zalog, vključno s prenosi in prilagoditvami zalog.
- Nastavitev samodejnih obvestil o nizkih stanjih zalog in generiranje naročilnic, ko je potrebno dopolnjevanje zalog.
- Ponujanje zmogljivosti skeniranja črtnih kod za učinkovito upravljanje zalog.
- Omogočanje uporabnikom razvrščanje izdelkov, določanje atributov in nastavitev cenovnih informacij.

## Finančno upravljanje:

- Uvedba sistema glavne knjige za sledenje finančnim transakcijam, vključno s stroški in prihodki.
- Upravljanje terjatev in obveznosti, vključno z izdajanjem računov in sledenjem plačil.
- Generiranje finančnih poročil, vključno z bilancami stanja in izkazi uspeha.
- Integracija s priljubljeno računovodsko programsko opremo za nemoteno finančno upravljanje.

## Upravljanje projektov:

- Zagotavljanje sposobnosti upravljanja projektov, ki omogoča uporabnikom ustvarjanje in sledenje projektom.
- Dodeljevanje nalog članom ekipe, določanje rokov in spremljanje napredka.
- Dodeljevanje virov in spremljanje stroškov projekta.
- Ponujanje funkcij sodelovanja, kot je deljenje dokumentov in komunikacija v realnem času.

## Poročanje in analitika:

- Generiranje celovitih poročil in analitike o različnih vidikih poslovanja.
- Zagotavljanje prilagodljivih nadzornih plošč za spremljanje ključnih kazalnikov uspešnosti (KPI).
- Omogočanje uporabnikom določanje prilagojenih poročil glede na posebne zahteve.
- Uvedba tehnike vizualizacije podatkov za prikazovanje informacij na privlačen način.

## Integracija in prilagajanje:

- Omogočanje integracije s priljubljenimi aplikacijami tretjih oseb ali vmesniki API, kot so orodja za e-poštni marketing ali platforme CRM.
- Dovoljevanje prilagajanja funkcionalnosti in izgleda aplikacije glede na posebne poslovne potrebe.
- Zagotavljanje API-ja ali spletnih kaveljčkov za olajšanje izmenjave podatkov med ERP in CRM aplikacijo ter drugimi sistemi.

## Uporabniku prijazen vmesnik:

- Oblikovanje intuitivnega, odzivnega in uporabniku prijaznega vmesnika z uporabo React.js in Ant Design.
- Uvedba enostavno uporabnih navigacijskih menijev, funkcionalnosti iskanja in filtrov.
- Zagotavljanje doslednega in privlačnega uporabniškega vmesnika na različnih napravah in zaslonih.