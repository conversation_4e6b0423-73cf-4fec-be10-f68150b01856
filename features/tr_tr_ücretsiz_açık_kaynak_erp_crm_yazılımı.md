# IDURAR Açık Kaynaklı ERP ve CRM Yazılımı

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Web sitesi: [https://www.idurarapp.com](https://www.idurarapp.com)

## Yazılım Yığını

IDURAR Ücretsiz açık kaynaklı erp ve crm uygulaması, "mern-stack" üzerine kuruludur: Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Kullanıcı Yönetimi:

- Yöneticilere kullanıcı hesapları oluşturma, düzenleme ve silme izni verir.
- Kullanıcı izinlerini yönetmek için rol tabanlı erişim kontrolü uygular.
- Güvenli erişimi sağlamak için kimlik doğrulama ve yetkilendirme mekanizmaları sağlar.

## Müşteri İlişkileri Yönetimi (CRM):

- Kullanıcıların potansiyel satış fırsatlarını takip etmek için müşteri adayları ve müşteriler için iletişim kayıtları oluşturmasına ve yönetmesine olanak tanır.
- Potansiyel satış fırsatlarını takip etmek ve değerlendirmek için lead jenerasyon ve nitelendirme işlevlerini uygular.
- E-postalar, telefon görüşmeleri ve toplantılar da dahil olmak üzere müşteri iletişim geçmişini yönetmek için araçlar sağlar.
- Kullanıcıların randevuları planlamasına ve müşterilere bildirim veya hatırlatıcı göndermesine izin verir.

## Satış Yönetimi:

- Kullanıcıların belirli müşterilerle ilişkilendirerek satış siparişleri oluşturmasına ve yönetmesine izin verir.
- Her satıştan sonra ürün mevcudiyetini kontrol etmek ve stok seviyelerini güncellemek için envanter takibi yapar.
- Faturalar oluşturur ve popüler ödeme geçitleriyle ödeme entegrasyonunu yönetir.
- Satış performansını izlemek ve trendleri analiz etmek için panolar ve raporlar sağlar.

## Satın Alma Yönetimi:

- Kullanıcıların miktarı ve istenen ürünleri belirterek satın alma siparişleri oluşturmasına ve yönetmesine izin verir.
- Tedarikçi bilgilerini takip eder ve tedarikçi ilişkilerini yönetir.
- Malzemeleri teslim alır ve envanter seviyelerini buna göre günceller.
- Satın alma faturalarını ve tedarikçilere yapılan ödemeleri yönetir.

## Stok Yönetimi:

- Stok transferleri ve düzeltmeler de dahil olmak üzere stok seviyelerini yönetmek ve takip etmek için araçlar sağlar.
- Düşük stok seviyeleri için otomatik bildirimler sağlar ve yeniden stoklama gerektiğinde satın alma siparişleri oluşturur.
- Verimli stok yönetimi için barkod tarama yetenekleri sunar.
- Kullanıcılara ürünleri kategorize etme, öznitelikleri tanımlama ve fiyatlandırma bilgilerini belirleme imkanı sunar.

## Finansal Yönetim:

- Giderler ve gelirler de dahil olmak üzere finansal işlemleri takip etmek için genel muhasebe sistemi uygular.
- Tahsilat ve ödeme takibi de dahil olmak üzere alacak ve borç hesaplarını yönetir.
- Bilanço ve gelir tablosu da dahil olmak üzere finansal raporlar oluşturur.
- Sorunsuz finansal yönetim için popüler muhasebe yazılımlarıyla entegrasyon sağlar.

## Proje Yönetimi:

- Kullanıcılara projeler oluşturma ve takip etme yeteneği sağlar.
- Görevleri takım üyelerine atar, son teslim tarihlerini belirler ve ilerlemeyi izler.
- Kaynakları tahsis eder ve proje giderlerini takip eder.
- Belge paylaşımı ve anlık iletişim gibi işbirliği özellikleri sunar.

## Raporlama ve Analitik:

- İşletmenin çeşitli yönleriyle ilgili kapsamlı raporlar ve analitikler oluşturur.
- Anahtar performans göstergelerini (KPI'lar) izlemek için özelleştirilebilir panolar sağlar.
- Kullanıcıların belirli gereksinimlere dayalı özel raporlar tanımlamasına izin verir.
- Bilgileri görsel olarak çekici bir şekilde sunmak için veri görselleştirme tekniklerini uygular.

## Entegrasyon ve Özelleştirme:

- E-posta pazarlama araçları veya CRM platformları gibi popüler üçüncü taraf uygulamalar veya API'larla entegrasyonu sağlar.
- Uygulamanın işlevselliğini ve görünümünü belirli iş gereksinimlerine göre özelleştirmeye izin verir.
- ERP ve CRM uygulaması ile diğer sistemler arasında veri alışverişini kolaylaştırmak için bir API veya webhooks sağlar.

## Kullanıcı Dostu Arayüz:

- React.js ve Ant Design kullanarak sezgisel, duyarlı ve kullanıcı dostu bir arayüz tasarlar.
- Kolay kullanımlı gezinme menüleri, arama işlevleri ve filtreler uygular.
- Farklı cihazlar ve ekran boyutları arasında tutarlı ve görsel olarak çekici bir kullanıcı arayüzü sağlar.