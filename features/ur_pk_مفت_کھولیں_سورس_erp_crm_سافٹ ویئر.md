# IDURAR اوپن سورس ERP & CRM سافٹ ویئر

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
ڈیمو: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
ویب سائٹ: [https://www.idurarapp.com](https://www.idurarapp.com)

## سافٹ ویئر اسٹیک

IDURAR مفت اوپن سورس erp & crm ایپ، "mern-stack" پر مبنی ہے: Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## صارف کا نظم و ضبط:

- ایڈمنسٹریٹرز کو صارف اکاؤنٹس تشکیل، ترمیم اور حذف کرنے کی اجازت دیں۔
- صارف کی اجازت کو مدیریت کرنے کے لئے کردار پر مبنی رسائی کنٹرول پیش کریں۔
- محفوظ رسائی کی یقینیت کے لئے تصدیق اور اجازت کے آلات فراہم کریں۔

## گاہک تعلقات کی مدیریت (CRM):

- صارفوں کو لیڈز، پروسپیکٹس اور گاہکوں کے لئے رابطہ ریکارڈ تشکیل اور مدیریت کرنے کی اجازت دیں۔
- فروختی مواقع کی پیشگوئی اور تصدیق کی کی قابلیتیں پیش کریں۔
- گاہک کے ساتھ رابطے کی تاریخ کو مدیریت کرنے کے لئے آلات فراہم کریں، جس میں ای میلز، کالز اور میٹنگز شامل ہوں۔
- صارفوں کو ملاقاتوں کا شیڈول بنانے اور گاہکوں کو نوٹیفکیشن یا یاد دلانے کی اجازت دیں۔

## فروخت کی مدیریت:

- صارفوں کو فروخت کی آرڈرز تشکیل اور مدیریت کرنے کی اجازت دیں، انہیں خاص گاہکوں سے منسلک کریں۔
- موجودہ میں مصنوعات کی دستیابیت کی جانچ پڑتال اور ہر فروخت کے بعد اسٹاک کی سطحوں کو اپ ڈیٹ کریں۔
- انوائسز بنائیں اور مقبول ادائیگی گیٹ وے کے ساتھ ادائیگی تعامل کو سنبھالیں۔
- فروخت کی کارکردگی کا نظارہ کرنے اور روایاتی خلاصوں کا تجزیہ کرنے کے لئے ڈیش بورڈز اور رپورٹس فراہم کریں۔

## خریداری کی مدیریت:

- صارفوں کو خریداری کی آرڈرز تشکیل اور مدیریت کرنے کی اجازت دیں، مقدار اور مطلوبہ مصنوعات کی وضاحت کریں۔
- سپلائر کی معلومات کی پیچیدگی کو ٹریک کریں اور سپلائر تعلقات کا نظم و ضبط کریں۔
- سامان کو وصول کریں اور اسٹاک کی سطحوں کو مطابقت کے ساتھ اپ ڈیٹ کریں۔
- خریداری کی انوائسز اور سپلائرز کو ادائیگی کا سنبھالنا کریں۔

## انوائنٹری مینجمنٹ:

- انوائنٹری کی سطحوں کو مینجمنٹ اور ٹریک کرنے کے لئے آلات فراہم کریں، جن میں اسٹاک ٹرانسفر اور ایڈجسٹمنٹ شامل ہوں۔
- کم اسٹاک کی اطلاعات کے لئے خودکار نوٹیفکیشنز کی تشکیل کریں اور جب بھی اسٹاک کی دوبارہ تعمیر کی ضرورت ہو تو خریداری کے آرڈرز بنائیں۔
- کارآمد انوائسٹری مینجمنٹ کے لئے بارکوڈ اسکیننگ کی صلاحیت فراہم کریں۔
- صارفوں کو مصنوعات کا زمرہ بنانے، خصوصیات تعریف کرنے اور قیمتی معلومات تعین کرنے کی اجازت دیں۔

## مالی مینجمنٹ:

- مالی لیجر سسٹم کو مدیریت کریں تاکہ مالی لین دین کے لئے معاملات کی ٹریکنگ ہو سکے، شامل ہیں اخراجات اور آمدنی۔
- وصولی اور ادائیگیوں کی مدیریت کریں، ان میں انوائسنگ اور ادائیگی کی ٹریکنگ شامل ہوں۔
- مالی رپورٹس تیار کریں، شامل ہیں بیلنس شیٹس اور انکم اسٹیٹمنٹس۔
- مقبول مالی مینجمنٹ کے لئے مشہور اکاؤنٹنگ سافٹ ویئر کے ساتھ انٹیگریشنٹ کریں۔

## پروجیکٹ مینجمنٹ:

- پروجیکٹ مینجمنٹ کی صلاحیتیں فراہم کریں، صارفوں کو پروجیکٹس تشکیل اور ٹریک کرنے کی اجازت دیں۔
- ٹیم کے رکنوں کو ٹاسکس کا تفویض کریں، مہلکے تعین کریں اور پیش رفت کا نظارہ کریں۔
- وسائل کو تقسیم کریں اور پروجیکٹ خرچ کی ٹریکنگ کریں۔
- دستاویزات کی شیئرنگ اور ریل ٹائم کمیونیکیشن جیسی تعاونی خصوصیات فراہم کریں۔

## رپورٹنگ اور تجزیہ:

- کاروبار کے مختلف پہلوؤں پر مکمل رپورٹس اور تجزیہ تیار کریں۔
- کلیدی کارکردگی اشاریوں (KPIs) کا نظارت کرنے کے لئے تخصیص پذیر ڈیش بورڈز فراہم کریں۔
- صارفوں کو مخصوص ضروریات پر مبنی کسٹم رپورٹس کی تعریف کرنے کی اجازت دیں۔
- معلومات کو خوبصورتی سے پیش کرنے کے لئے ڈیٹا وزیوالائزیشن تکنیکس کو پیش کریں۔

## انٹیگریشن اور کسٹمائزیشن:

- مقبول تیسری طرف کے اہم اہم ایپلیکیشنز یا APIز کے ساتھ انٹیگریشن فراہم کریں، مثلاً ای میل مارکیٹنگ ٹولز یا CRM پلیٹ فارمز۔
- تفویض کی تشکیل اور دکان کی ظاہریت کو مخصوص کاروباری ضروریات پر مبنی کسٹمائزیشن کی اجازت دیں۔
- ERP & CRM ایپ اور دیگر سسٹمز کے درمیان ڈیٹا تبادلے کو آسان بنانے کے لئے ای پی آئی یا ویب ہکس فراہم کریں۔

## صارف دوستانہ انٹرفیس:

- React.js اور Ant Design کا استعمال کرتے ہوئے سمجھدار، ریسپانسیو اور صارف دوستانہ انٹرفیس ڈیزائن کریں۔
- استعمال میں آسان نیویگیشن مینوز، تلاش کی صلاحیتیں اور فلٹرز کو پیش کریں۔
- مختلف آلات اور اسکرین سائزز پر مستقل اور خوبصورت یو آئی یو کی یقینیت فراہم کریں۔