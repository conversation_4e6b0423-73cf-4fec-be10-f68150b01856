# IDURAR Open-Source ERP & CRM Softver

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Veb sajt: [https://www.idurarapp.com](https://www.idurarapp.com)

## Softverski skup

IDURAR Besplatan otvoreni ERP i CRM softver, baziran na "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Upravljanje korisnicima:

- Dozvolite administratorima da kreiraju, uređuju i brišu korisničke naloge.
- Implementirajte kontrolu pristupa zasnovanu na ulogama kako biste upravljali korisničkim dozvolama.
- Pružite mehanizme za autentifikaciju i autorizaciju kako biste osigurali siguran pristup.

## Upravljanje odnosima sa klijentima (CRM):

- Omogućite korisnicima da kreiraju i upravljaju kontaktima za potencijalne klijente i postojeće klijente.
- Implementirajte funkcionalnosti generisanja i kvalifikacije potencijalnih prodajnih prilika.
- Pružite alate za upravljanje istorijom komunikacije sa klijentima, uključujući e-poštu, pozive i sastanke.
- Dozvolite korisnicima da zakazuju termine i šalju obaveštenja ili podsetnike klijentima.

## Upravljanje prodajom:

- Dozvolite korisnicima da kreiraju i upravljaju prodajnim nalozima, povezujući ih sa određenim klijentima.
- Implementirajte praćenje inventara radi provere dostupnosti proizvoda i ažuriranja nivoa zaliha nakon svake prodaje.
- Generišite fakture i integrišite se sa popularnim platnim gateway-ima.
- Pružite kontrolne table i izveštaje za praćenje prodajne performanse i analizu trendova.

## Upravljanje nabavkom:

- Dozvolite korisnicima da kreiraju i upravljaju narudžbinama nabavke, specificirajući količinu i željene proizvode.
- Pratite informacije o dobavljačima i upravljajte odnosima sa dobavljačima.
- Primajte robu i ažurirajte nivoe zaliha prema tome.
- Upravljajte fakturama nabavke i plaćanjima dobavljačima.

## Upravljanje zalihama:

- Pružite alate za upravljanje i praćenje nivoa zaliha, uključujući prenos i prilagođavanje zaliha.
- Postavite automatska obaveštenja za niske nivoe zaliha i generišite narudžbe nabavke kada je potrebno dopunjavanje zaliha.
- Ponudite mogućnosti skeniranja barkoda za efikasno upravljanje zalihama.
- Omogućite korisnicima da kategorizuju proizvode, definišu atribute i postave informacije o cenama.

## Finansijsko upravljanje:

- Implementirajte sistem glavne knjige za praćenje finansijskih transakcija, uključujući troškove i prihode.
- Upravljajte potraživanjima i obavezama, uključujući fakturisanje i praćenje plaćanja.
- Generišite finansijske izveštaje, uključujući bilanse stanja i izveštaje o prihodima.
- Integrišite se sa popularnim softverima za računovodstvo radi besprekornog finansijskog upravljanja.

## Upravljanje projektima:

- Pružite mogućnosti upravljanja projektima, omogućavajući korisnicima da kreiraju i prate projekte.
- Dodeljujte zadatke članovima tima, postavljajte rokove i pratite napredak.
- Alocirajte resurse i pratite troškove projekta.
- Ponudite funkcionalnosti saradnje, kao što su deljenje dokumenata i komunikacija u realnom vremenu.

## Izveštavanje i analitika:

- Generišite sveobuhvatne izveštaje i analitiku o različitim aspektima poslovanja.
- Pružite prilagodljive kontrolne table za praćenje ključnih pokazatelja performansi (KPI-jeva).
- Dozvolite korisnicima da definišu prilagođene izveštaje na osnovu specifičnih zahteva.
- Implementirajte tehnike vizualizacije podataka radi prikaza informacija na vizuelno privlačan način.

## Integracija i prilagođavanje:

- Omogućite integraciju sa popularnim aplikacijama trećih strana ili API-jima, kao što su alati za e-poštu ili platforme za CRM.
- Dozvolite prilagođavanje funkcionalnosti i izgleda aplikacije prema specifičnim poslovnim potrebama.
- Pružite API ili webhooks kako biste olakšali razmenu podataka između ERP i CRM aplikacije i drugih sistema.

## Korisnički prijateljski interfejs:

- Dizajnirajte intuitivan, responsivan i korisnički prijateljski interfejs koristeći React.js i Ant Design.
- Implementirajte jednostavne navigacione menije, funkcionalnosti pretrage i filtere.
- Osigurajte dosledan i vizuelno privlačan korisnički interfejs na različitim uređajima i veličinama ekrana.