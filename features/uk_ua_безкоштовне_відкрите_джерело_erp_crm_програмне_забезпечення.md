# IDURAR Відкритий ERP та CRM-софт

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Демо: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Веб-сайт: [https://www.idurarapp.com](https://www.idurarapp.com)

## Стек програмного забезпечення

IDURAR Безкоштовний відкритий ERP та CRM-додаток, заснований на "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Управління користувачами:

- Дозволяє адміністраторам створювати, редагувати та видаляти облікові записи користувачів.
- Реалізувати контроль доступу на основі ролей для управління дозволами користувачів.
- Забезпечити механізми аутентифікації та авторизації для забезпечення безпечного доступу.

## Управління взаємовідносинами з клієнтами (CRM):

- Дозволяє користувачам створювати та управляти контактними записами для потенційних клієнтів та клієнтів.
- Реалізувати функціонал залучення та кваліфікації лідів для відстеження потенційних продажних можливостей.
- Надати інструменти для управління історією комунікації з клієнтами, включаючи електронну пошту, дзвінки та зустрічі.
- Дозволяє користувачам планувати зустрічі та надсилати сповіщення або нагадування клієнтам.

## Управління продажами:

- Дозволяє користувачам створювати та управляти замовленнями на продаж, пов'язуючи їх з конкретними клієнтами.
- Реалізувати відстеження запасів для перевірки наявності продуктів та оновлення рівня запасів після кожної продажу.
- Генерувати рахунки та обробляти інтеграцію платежів з популярними платіжними шлюзами.
- Надати панелі управління та звіти для контролю продажів та аналізу тенденцій.

## Управління закупівлями:

- Дозволяє користувачам створювати та управляти замовленнями на закупівлю, вказуючи кількість та бажані товари.
- Відстежувати інформацію про постачальників та управляти взаєминами з постачальниками.
- Отримувати товари та оновлювати рівні запасів відповідно.
- Обробляти покупку рахунків та платежі постачальникам.

## Управління запасами:

- Надати інструменти для управління та відстеження рівнів запасів, включаючи переміщення та коригування запасів.
- Налаштувати автоматичні сповіщення про низькі рівні запасів та генерувати замовлення на закупівлю при необхідності поповнення запасів.
- Надати можливості сканування штрих-кодів для ефективного управління запасами.
- Дозволити користувачам категоризувати продукти, визначати атрибути та встановлювати інформацію про ціни.

## Фінансове управління:

- Реалізувати систему головної книги для відстеження фінансових транзакцій, включаючи витрати та доходи.
- Управляти дебіторською та кредиторською заборгованістю, включаючи виставлення рахунків та відстеження платежів.
- Генерувати фінансові звіти, включаючи баланси та звіти про прибутки.
- Інтегрувати з популярними бухгалтерськими програмами для безпроблемного фінансового управління.

## Управління проектами:

- Надати можливості управління проектами, що дозволяють користувачам створювати та відстежувати проекти.
- Призначати завдання членам команди, встановлювати терміни виконання та контролювати прогрес.
- Розподіляти ресурси та відстежувати витрати на проект.
- Надавати можливості співпраці, такі як спільний доступ до документів та комунікація в реальному часі.

## Звітність та аналітика:

- Генерувати комплексні звіти та аналітику з різних аспектів бізнесу.
- Надавати настроювані панелі управління для контролю ключових показників ефективності (KPI).
- Дозволяти користувачам визначати власні звіти на основі конкретних вимог.
- Реалізувати техніки візуалізації даних для представлення інформації в привабливій візуальній формі.

## Інтеграція та настроювання:

- Забезпечити можливість інтеграції з популярними сторонніми додатками або API, такими як інструменти електронного маркетингу поштою або платформи CRM.
- Дозволити настроювання функціональності та зовнішнього вигляду додатка на основі конкретних потреб бізнесу.
- Надати API або вебхуки для сприяння обміну даними між ERP та CRM-додатком та іншими системами.

## Інтерфейс, зручний для користувачів:

- Розробка інтуїтивно зрозумілого, адаптивного та зручного для користувачів інтерфейсу з використанням React.js та Ant Design.
- Реалізація простого використання навігаційних меню, функцій пошуку та фільтрів.
- Забезпечення однорідного та привабливого візуального інтерфейсу на різних пристроях та розмірах екрану.