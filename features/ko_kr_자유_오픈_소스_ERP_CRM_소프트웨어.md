# IDURAR 오픈 소스 ERP & CRM 소프트웨어

GitHub : [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
데모 : [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
웹사이트 : [https://www.idurarapp.com](https://www.idurarapp.com)

## 소프트웨어 스택

IDURAR 무료 오픈 소스 erp & crm 앱, "mern-stack"을 기반으로 함: Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## 사용자 관리:

- 관리자가 사용자 계정을 생성, 편집 및 삭제할 수 있도록 허용합니다.
- 역할 기반의 액세스 제어를 구현하여 사용자 권한을 관리합니다.
- 안전한 액세스를 보장하기 위해 인증 및 권한 부여 메커니즘을 제공합니다.

## 고객 관계 관리 (CRM):

- 사용자가 잠재 고객, 전망 고객 및 고객을 위한 연락처 기록을 생성 및 관리할 수 있도록 합니다.
- 잠재적인 영업 기회를 추적하기 위한 리드 생성 및 자격 인증 기능을 구현합니다.
- 이메일, 전화 및 회의 등을 포함한 고객과의 커뮤니케이션 기록을 관리하는 도구를 제공합니다.
- 사용자가 약속을 예약하고 고객에게 알림이나 리마인더를 보낼 수 있도록 합니다.

## 영업 관리:

- 사용자가 영업 주문을 생성하고 관리할 수 있도록 하며, 이를 특정 고객과 연결합니다.
- 제품 가용성을 확인하고 각 판매 후 재고 수준을 업데이트하기 위해 재고 추적을 구현합니다.
- 인기 있는 결제 게이트웨이와의 결제 통합을 처리하고 송장을 생성합니다.
- 판매 성과를 모니터링하고 동향을 분석하기 위한 대시보드 및 보고서를 제공합니다.

## 구매 관리:

- 사용자가 수량과 원하는 제품을 지정하여 구매 주문을 생성 및 관리할 수 있도록 합니다.
- 공급업체 정보를 추적하고 공급업체 관계를 관리합니다.
- 상품을 수령하고 재고 수준을 업데이트합니다.
- 공급업체에 대한 구매 송장 및 지불을 처리합니다.

## 재고 관리:

- 재고 수준을 관리하고 추적하기 위한 도구를 제공합니다. 이는 재고 이동 및 조정을 포함합니다.
- 재고가 부족한 경우 자동 알림 설정 및 보충이 필요한 경우 구매 주문 생성을 설정합니다.
- 효율적인 재고 관리를 위한 바코드 스캐닝 기능을 제공합니다.
- 사용자가 제품을 분류하고 속성을 정의하며 가격 정보를 설정할 수 있도록 합니다.

## 재무 관리:

- 비용 및 수익을 포함한 재무 거래를 추적하기 위한 일반 원장 시스템을 구현합니다.
- 매출채권 및 매입채무를 관리하며, 송장 및 지불 추적을 포함합니다.
- 재무 보고서를 생성하며, 재무 상태표와 손익 계산서를 포함합니다.
- 인기 있는 회계 소프트웨어와의 원활한 재무 관리를 위해 통합합니다.

## 프로젝트 관리:

- 사용자가 프로젝트를 생성하고 추적할 수 있는 프로젝트 관리 기능을 제공합니다.
- 팀 멤버에게 작업을 할당하고 마감일을 설정하며 진행 상황을 모니터링할 수 있습니다.
- 자원을 할당하고 프로젝트 비용을 추적합니다.
- 문서 공유 및 실시간 커뮤니케이션과 같은 협업 기능을 제공합니다.

## 보고 및 분석:

- 비즈니스의 다양한 측면에 대한 포괄적인 보고서 및 분석을 생성합니다.
- 핵심 성과 지표 (KPI)를 모니터링하기 위한 사용자 정의 대시보드를 제공합니다.
- 특정 요구 사항에 기반한 사용자 정의 보고서를 정의할 수 있습니다.
- 정보를 시각적으로 표현하기 위해 데이터 시각화 기법을 구현합니다.

## 통합 및 사용자 정의:

- 이메일 마케팅 도구나 CRM 플랫폼과 같은 인기 있는 타사 애플리케이션 또는 API와의 통합을 가능하게 합니다.
- 특정 비즈니스 요구 사항에 따라 앱의 기능과 외관을 사용자 정의할 수 있습니다.
- ERP & CRM 앱과 다른 시스템 간의 데이터 교환을 용이하게 하기 위해 API 또는 웹훅을 제공합니다.

## 사용자 친화적 인터페이스:

- React.js와 Ant Design을 사용하여 직관적이고 반응형이며 사용자 친화적인 인터페이스를 디자인합니다.
- 사용하기 쉬운 탐색 메뉴, 검색 기능 및 필터를 구현합니다.
- 다양한 기기와 화면 크기에서 일관되고 시각적으로 매력적인 UI를 보장합니다.