# IDURAR Open-Source ERP & CRM Tarkvara

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Veebisait: [https://www.idurarapp.com](https://www.idurarapp.com)

## Tarkvara Stack

IDURAR Tasuta avatud lähtekoodiga erp & crm rakendus, mis põhineb "mern-stack" tehnoloogial: Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Kasutajate haldamine:

- Võimaldab administraatoritel luua, muuta ja kustutada kasutajakontosid.
- Rakenda rollipõhine juurdepääsu kontroll kasutajaõiguste haldamiseks.
- Paku autentimis- ja autoriseerimismehhanisme turvalise juurdepääsu tagamiseks.

## Kliendisuhte haldamine (CRM):

- Võimaldab kasutajatel luua ja hallata kontaktisikute andmeid müügivihjete, potentsiaalsete klientide ja klientide jaoks.
- Rakenda müügivihjete genereerimise ja kvalifitseerimise funktsionaalsust, et jälgida potentsiaalseid müügivõimalusi.
- Paku tööriistu kliendikommunikatsiooni ajaloo haldamiseks, sealhulgas e-kirjad, kõned ja kohtumised.
- Võimaldab kasutajatel planeerida kohtumisi ja saata teateid või meeldetuletusi klientidele.

## Müügihaldus:

- Võimaldab kasutajatel luua ja hallata müügiordereid, sidudes need konkreetsete klientidega.
- Rakenda laojäägi jälgimist, et kontrollida toodete saadavust ja värskendada varude taset pärast igat müüki.
- Genereeri arveid ja hõlbusta makse integreerimist populaarsete makseväravatega.
- Paku juhtpaneeli ja aruandeid müügitulemuste jälgimiseks ja suundumuste analüüsimiseks.

## Ostuhaldus:

- Võimaldab kasutajatel luua ja hallata ostutellimusi, määrates koguse ja soovitud tooted.
- Jälgige tarnija teavet ja haldage tarnijasuhet.
- Vastu võtke kaupu ja värskendage vastavalt varude taset.
- Hõlbusta ostuarveid ja makseid tarnijatele.

## Varude haldamine:

- Paku tööriistu varude tasemete haldamiseks ja jälgimiseks, sealhulgas varude ülekandeid ja kohandusi.
- Seadista automaatseid teatisi madalate varude tasemete kohta ja genereeri ostutellimusi, kui on vaja täiendavat varustust.
- Paku vöötkoodi skannimise võimalusi tõhusaks varude haldamiseks.
- Võimaldab kasutajatel kategoriseerida tooteid, määratleda atribuute ja seada hinnateavet.

## Finantsjuhtimine:

- Rakenda pearaamatute süsteem finantstehingute jälgimiseks, sealhulgas kulude ja tulude kohta.
- Halla saadaolevaid ja saamataolevaid kontosid, sealhulgas arvete koostamine ja maksete jälgimine.
- Genereeri finantsaruandeid, sealhulgas bilanssi ja kasumiaruannet.
- Integreeru populaarsete raamatupidamistarkvaradega, et tagada sujuv finantsjuhtimine.

## Projektihaldus:

- Paku projektihalduse võimalusi, mis võimaldavad kasutajatel luua ja jälgida projekte.
- Määra ülesandeid meeskonnaliikmetele, seadista tähtajad ja jälgi edenemist.
- Jaota ressursse ja jälgige projekti kulusid.
- Paku koostöövõimalusi, nagu dokumentide jagamine ja reaalajas suhtlus.

## Aruandlus ja analüütika:

- Genereeri põhjalikke aruandeid ja analüüse ettevõtte erinevate aspektide kohta.
- Paku kohandatavaid juhtpaneeli, et jälgida olulisi jõudlusnäitajaid (KPI-d).
- Võimalda kasutajatel määratleda kohandatud aruandeid vastavalt konkreetsetele nõuetele.
- Rakenda andmete visualiseerimise tehnikaid, et esitada teavet visuaalselt atraktiivsel viisil.

## Integreerimine ja kohandamine:

- Võimalda integreerimist populaarsete kolmanda osapoole rakenduste või API-dega, nagu e-posti turundustööriistad või CRM platvormid.
- Lubage rakenduse funktsionaalsuse ja välimuse kohandamist vastavalt konkreetsetele ärinõuetele.
- Paku API-d või veebikonksusid, et hõlbustada andmevahetust ERP & CRM rakenduse ja teiste süsteemide vahel.

## Kasutajasõbralik liides:

- Kujundage intuitiivne, reageeriv ja kasutajasõbralik liides, kasutades React.js ja Ant Designi.
- Rakenda lihtsasti kasutatavaid navigeerimismenüüsid, otsingufunktsioone ja filtreid.
- Taga järjepidev ja visuaalselt atraktiivne kasutajaliides erinevatel seadmetel ja ekraanisuurustel.