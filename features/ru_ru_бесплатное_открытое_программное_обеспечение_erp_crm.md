# IDURAR Открытое ERP и CRM программное обеспечение

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Демо: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Веб-сайт: [https://www.idurarapp.com](https://www.idurarapp.com)

## Стек программного обеспечения

IDURAR Бесплатное открытое ERP и CRM приложение, основанное на "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Управление пользователями:

- Позволяет администраторам создавать, редактировать и удалять учетные записи пользователей.
- Реализует управление доступом на основе ролей для управления правами пользователей.
- Обеспечивает аутентификацию и авторизацию для обеспечения безопасного доступа.

## Управление взаимоотношениями с клиентами (CRM):

- Позволяет пользователям создавать и управлять контактными записями для потенциальных клиентов и клиентов.
- Реализует функциональность генерации и квалификации лидов для отслеживания потенциальных продаж.
- Предоставляет инструменты для управления историей коммуникации с клиентами, включая электронные письма, звонки и встречи.
- Позволяет пользователям назначать встречи и отправлять уведомления или напоминания клиентам.

## Управление продажами:

- Позволяет пользователям создавать и управлять заказами, связывая их с конкретными клиентами.
- Реализует отслеживание запасов для проверки наличия продукции и обновления уровней запасов после каждой продажи.
- Генерирует счета и обрабатывает интеграцию платежей с популярными платежными шлюзами.
- Предоставляет панели управления и отчеты для мониторинга результативности продаж и анализа тенденций.

## Управление закупками:

- Позволяет пользователям создавать и управлять заказами на закупку, указывая количество и необходимые продукты.
- Отслеживает информацию о поставщиках и управляет взаимоотношениями с поставщиками.
- Принимает товары и соответствующим образом обновляет уровни запасов.
- Обрабатывает счета на закупку и платежи поставщикам.

## Управление запасами:

- Предоставляет инструменты для управления и отслеживания уровней запасов, включая перемещение и корректировку запасов.
- Настраивает автоматические уведомления о низком уровне запасов и генерирует заказы на закупку при необходимости пополнения запасов.
- Предлагает возможности сканирования штрих-кодов для эффективного управления запасами.
- Позволяет пользователям классифицировать продукты, определять атрибуты и устанавливать информацию о ценообразовании.

## Финансовое управление:

- Реализует систему главной книги для отслеживания финансовых операций, включая расходы и доходы.
- Управляет дебиторской и кредиторской задолженностью, включая выставление счетов и отслеживание платежей.
- Генерирует финансовые отчеты, включая баланс и отчет о доходах.
- Интегрируется с популярными программами бухгалтерии для беспроблемного финансового управления.

## Управление проектами:

- Предоставляет возможности управления проектами, позволяя пользователям создавать и отслеживать проекты.
- Назначает задачи участникам команды, устанавливает сроки и контролирует прогресс.
- Распределяет ресурсы и отслеживает затраты на проект.
- Предлагает функции совместной работы, такие как обмен документами и общение в режиме реального времени.

## Отчетность и аналитика:

- Генерирует подробные отчеты и аналитику по различным аспектам бизнеса.
- Предоставляет настраиваемые панели управления для отслеживания ключевых показателей эффективности (KPI).
- Позволяет пользователям определять настраиваемые отчеты на основе конкретных требований.
- Реализует техники визуализации данных для представления информации в привлекательном визуальном виде.

## Интеграция и настройка:

- Обеспечивает интеграцию с популярными сторонними приложениями или API, такими как инструменты электронного маркетинга или платформы CRM.
- Позволяет настраивать функциональность и внешний вид приложения на основе конкретных потребностей бизнеса.
- Предоставляет API или вебхуки для облегчения обмена данными между ERP и CRM приложением и другими системами.

## Пользовательский интерфейс, удобный для использования:

- Разрабатывает интуитивно понятный, отзывчивый и удобный для пользователя интерфейс с использованием React.js и Ant Design.
- Реализует простые в использовании навигационные меню, функции поиска и фильтры.
- Обеспечивает согласованный и привлекательный визуальный интерфейс на различных устройствах и размерах экрана.