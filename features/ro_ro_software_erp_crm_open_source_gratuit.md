# IDURAR Software ERP și CRM Open-Source

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Website: [https://www.idurarapp.com](https://www.idurarapp.com)

## Stivă de software

IDURAR este o aplicație ERP și CRM open-source gratuită, bazată pe "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Managementul utilizatorilor:

- Permite administratorilor să creeze, editeze și șteargă conturi de utilizator.
- Implementează controlul accesului bazat pe roluri pentru gestionarea permisiunilor utilizatorilor.
- Furnizează mecanisme de autentificare și autorizare pentru a asigura un acces securizat.

## Managementul relațiilor cu clienții (CRM):

- Permite utilizatorilor să creeze și să gestioneze înregistrări de contact pentru clienți potențiali și existenți.
- Implementează funcționalități de generare și calificare a lead-urilor pentru a urmări oportunitățile potențiale de vânzări.
- Furnizează instrumente pentru gestionarea istoricului de comunicare cu clienții, inclusiv e-mailuri, apeluri și întâlniri.
- Permite utilizatorilor să programeze întâlniri și să trimită notificări sau mementouri clienților.

## Managementul vânzărilor:

- Permite utilizatorilor să creeze și să gestioneze comenzi de vânzare, asociindu-le cu clienți specifici.
- Implementează urmărirea inventarului pentru a verifica disponibilitatea produselor și a actualiza nivelurile de stoc după fiecare vânzare.
- Generează facturi și gestionează integrarea plăților cu gateway-uri populare de plată.
- Furnizează tablouri de bord și rapoarte pentru monitorizarea performanțelor de vânzări și analiza tendințelor.

## Managementul achizițiilor:

- Permite utilizatorilor să creeze și să gestioneze comenzi de achiziții, specificând cantitatea și produsele dorite.
- Urmărește informațiile furnizorului și gestionează relațiile cu furnizorii.
- Primește bunuri și actualizează nivelurile de inventar în consecință.
- Gestionează facturile de achiziții și plățile către furnizori.

## Managementul inventarului:

- Furnizează instrumente pentru gestionarea și urmărirea nivelurilor de inventar, inclusiv transferuri de stoc și ajustări.
- Configurează notificări automate pentru nivelurile scăzute de stoc și generează comenzi de achiziții atunci când este necesară reînnoirea stocului.
- Oferă capacități de scanare a codurilor de bare pentru gestionarea eficientă a inventarului.
- Permite utilizatorilor să clasifice produsele, să definească atribute și să seteze informații de preț.

## Managementul financiar:

- Implementează un sistem de registrul general pentru urmărirea tranzacțiilor financiare, inclusiv cheltuieli și venituri.
- Gestionează creanțele și datoriile, inclusiv facturare și urmărirea plăților.
- Generează rapoarte financiare, inclusiv bilanțuri și state de venituri.
- Integrează cu software-uri populare de contabilitate pentru gestionarea financiară fără probleme.

## Managementul proiectelor:

- Furnizează capacități de gestionare a proiectelor, permițând utilizatorilor să creeze și să urmărească proiecte.
- Alocă sarcini membrilor echipei, stabilește termene limită și monitorizează progresul.
- Alocați resurse și urmăriți cheltuielile proiectului.
- Oferă funcționalități de colaborare, cum ar fi partajarea de documente și comunicarea în timp real.

## Raportare și analiză:

- Generează rapoarte cuprinzătoare și analize asupra diferitelor aspecte ale afacerii.
- Furnizează tablouri de bord personalizabile pentru monitorizarea indicatorilor cheie de performanță (KPI).
- Permite utilizatorilor să definească rapoarte personalizate în funcție de cerințe specifice.
- Implementează tehnici de vizualizare a datelor pentru a prezenta informațiile într-un mod vizual atractiv.

## Integrare și personalizare:

- Permite integrarea cu aplicații terțe populare sau API-uri, cum ar fi instrumentele de marketing prin e-mail sau platformele CRM.
- Permite personalizarea funcționalității și aspectului aplicației în funcție de nevoile specifice ale afacerii.
- Furnizează o API sau webhook-uri pentru a facilita schimbul de date între aplicația ERP și CRM și alte sisteme.

## Interfață prietenoasă pentru utilizator:

- Proiectați o interfață intuitivă, receptivă și prietenoasă pentru utilizator folosind React.js și Ant Design.
- Implementați meniuri de navigare ușor de utilizat, funcționalități de căutare și filtre.
- Asigurați o interfață utilizator coerentă și vizual atractivă pe diferite dispozitive și dimensiuni de ecran.