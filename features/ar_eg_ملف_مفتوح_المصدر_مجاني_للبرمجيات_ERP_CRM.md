# IDURAR برنامج إدارة الموارد التخطيطية وعلاقات العملاء مفتوح المصدر

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
العرض التوضيحي: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
الموقع الإلكتروني: [https://www.idurarapp.com](https://www.idurarapp.com)

## تكوين البرنامج

IDURAR تطبيق ERP & CRM مفتوح المصدر ومجاني، يعتمد على "mern-stack" : Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## إدارة المستخدمين:

- السماح للمسؤولين بإنشاء وتحرير وحذف حسابات المستخدمين.
- تنفيذ نظام التحكم في الوصول المستند إلى الأدوار لإدارة أذونات المستخدمين.
- توفير آليات المصادقة والتفويض لضمان الوصول الآمن.

## إدارة علاقات العملاء (CRM):

- تمكين المستخدمين من إنشاء وإدارة سجلات الاتصال للعملاء المحتملين والعملاء.
- تنفيذ وظائف توليد العملاء المحتملين وتأهيلهم لتتبع فرص المبيعات المحتملة.
- توفير أدوات لإدارة تاريخ التواصل مع العملاء، بما في ذلك الرسائل البريدية والمكالمات والاجتماعات.
- السماح للمستخدمين بجدولة المواعيد وإرسال إشعارات أو تذكيرات للعملاء.

## إدارة المبيعات:

- السماح للمستخدمين بإنشاء وإدارة أوامر البيع، مرتبطة بعملاء محددين.
- تنفيذ تتبع المخزون للتحقق من توافر المنتج وتحديث مستويات المخزون بعد كل بيع.
- إنشاء الفواتير والتعامل مع تكامل الدفع مع بوابات الدفع الشهيرة.
- توفير لوحات المعلومات والتقارير لمراقبة أداء المبيعات وتحليل الاتجاهات.

## إدارة المشتريات:

- السماح للمستخدمين بإنشاء وإدارة أوامر الشراء، محددة الكمية والمنتجات المطلوبة.
- تتبع معلومات المورد وإدارة علاقات الموردين.
- استلام السلع وتحديث مستويات المخزون وفقًا لذلك.
- التعامل مع فواتير الشراء والمدفوعات للموردين.

## إدارة المخزون:

- توفير أدوات لإدارة وتتبع مستويات المخزون، بما في ذلك نقل المخزون وتعديلاته.
- إعداد إشعارات تلقائية لمستويات المخزون المنخفضة وإنشاء أوامر الشراء عند الحاجة لإعادة التزود.
- توفير قدرات مسح الباركود لإدارة المخزون بكفاءة.
- تمكين المستخدمين من تصنيف المنتجات وتحديد السمات وتحديد معلومات التسعير.

## إدارة المالية:

- تنفيذ نظام دفتر الأستاذ العام لتتبع المعاملات المالية، بما في ذلك المصروفات والإيرادات.
- إدارة الحسابات المدينة والحسابات الدائنة، بما في ذلك إصدار الفواتير وتتبع الدفعات.
- إنشاء تقارير مالية، بما في ذلك الميزانية العمومية وقوائم الدخل.
- تكامل مع برامج المحاسبة الشهيرة لإدارة مالية سلسة.

## إدارة المشاريع:

- توفير قدرات إدارة المشاريع، مما يتيح للمستخدمين إنشاء وتتبع المشاريع.
- تعيين المهام لأعضاء الفريق، وتحديد المواعيد النهائية، ومراقبة التقدم.
- تخصيص الموارد وتتبع مصاريف المشروع.
- توفير ميزات التعاون مثل مشاركة المستندات والاتصال في الوقت الحقيقي.

## التقارير والتحليلات:

- إنشاء تقارير وتحليلات شاملة حول جوانب مختلفة من العمل.
- توفير لوحات المعلومات قابلة للتخصيص لمراقبة مؤشرات الأداء الرئيسية.
- السماح للمستخدمين بتعريف تقارير مخصصة بناءً على متطلبات محددة.
- تنفيذ تقنيات تصور البيانات لتقديم المعلومات بطريقة جذابة بصريًا.

## التكامل والتخصيص:

- تمكين التكامل مع تطبيقات أو واجهات برمجة التطبيقات الخارجية الشهيرة، مثل أدوات التسويق عبر البريد الإلكتروني أو منصات CRM.
- السماح بتخصيص وظائف التطبيق ومظهره وفقًا لاحتياجات الأعمال المحددة.
- توفير واجهة برمجة التطبيقات (API) أو الويب هوكس لتسهيل تبادل البيانات بين تطبيق ERP & CRM والأنظمة الأخرى.

## واجهة سهلة الاستخدام:

- تصميم واجهة سهلة الاستخدام ومتجاوبة ومستخدمة باستخدام React.js و Ant Design.
- تنفيذ قوائم تنقل سهلة الاستخدام ووظائف البحث والتصفية.
- ضمان واجهة مستخدم متسقة وجذابة بصريًا عبر أجهزة مختلفة وأحجام الشاشة.