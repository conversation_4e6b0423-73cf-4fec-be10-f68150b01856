# IDURAR Atvērtā koda ERP un CRM programmatūra

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Mājaslapa: [https://www.idurarapp.com](https://www.idurarapp.com)

## Programmatūras tehnoloģiju komplekts

IDURAR bezmaksas atvērtā koda erp & crm lietotne, balstīta uz "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Lietotāju pārvaldība:

- Ļauj administratoriem izveidot, labot un dzēst lietotāja kontus.
- Ievieš lomu pamatotas piekļuves vadības funkcionalitāti, lai pārvaldītu lietotāju atļaujas.
- Nodrošina autentifikācijas un autorizācijas mehānismus, lai nodrošinātu drošu piekļuvi.

## Klientu attiecību pārvaldība (CRM):

- Ļauj lietotājiem izveidot un pārvaldīt kontaktu ierakstus potenciāliem pārdošanas iespējām un klientiem.
- Ievieš potenciālo pārdošanas iespēju ģenerēšanas un kvalifikācijas funkcionalitātes, lai sekotu līdzi potenciālajām pārdošanas iespējām.
- Nodrošina rīkus klientu komunikācijas vēstures pārvaldībai, ieskaitot e-pastus, zvanus un tikšanās.
- Ļauj lietotājiem ieplānot tikšanās un nosūtīt paziņojumus vai atgādinājumus klientiem.

## Pārdošanas pārvaldība:

- Ļauj lietotājiem izveidot un pārvaldīt pārdošanas pasūtījumus, tos saistot ar konkrētiem klientiem.
- Ievieš krājumu izsekošanu, lai pārbaudītu produktu pieejamību un atjauninātu krājuma līmeņus pēc katras pārdošanas.
- Ģenerē rēķinus un apstrādā maksājumu integrāciju ar populārām maksājumu vārtām.
- Nodrošina vadības panelus un pārskatus, lai uzraudzītu pārdošanas veiktspēju un analizētu tendences.

## Iepirkumu pārvaldība:

- Ļauj lietotājiem izveidot un pārvaldīt iepirkumu pasūtījumus, norādot daudzumu un vēlamās preces.
- Seko piegādātāja informācijai un pārvalda piegādātāju attiecības.
- Saņem preces un atjaunina krājumu līmenis atbilstoši.
- Apstrādā iepirkuma rēķinus un maksājumus piegādātājiem.

## Krājumu pārvaldība:

- Nodrošina rīkus, lai pārvaldītu un izsekotu krājumu līmeņus, ieskaitot krājuma pārvedumus un pielāgojumus.
- Iestatiet automātiskas paziņojumus par zemu krājumu līmeni un ģenerējiet iepirkumu pasūtījumus, ja nepieciešams atjaunot krājumus.
- Piedāvā skenēšanas iespējas efektīvai krājumu pārvaldībai.
- Ļauj lietotājiem kategorizēt produktus, definēt atribūtus un iestatīt cenu informāciju.

## Finanšu pārvaldība:

- Ievieš galveno grāmatvedības sistēmu, lai izsekotu finanšu darījumus, ieskaitot izdevumus un ienākumus.
- Pārvalda debitoru un kreditoru kontus, ieskaitot rēķinu izrakstīšanu un maksājumu izsekošanu.
- Ģenerē finanšu pārskatus, ieskaitot bilances un ieņēmumu paziņojumus.
- Integrējas ar populāro grāmatvedības programmatūru bez šķēršļiem finanšu pārvaldībai.

## Projektu pārvaldība:

- Nodrošina projektu pārvaldības iespējas, ļaujot lietotājiem izveidot un sekot līdzi projektiem.
- Piešķir uzdevumus komandas locekļiem, iestata termiņus un uzrauga progresu.
- Piešķir resursus un seko projektu izdevumiem.
- Piedāvā sadarbības funkcijas, piemēram, dokumentu kopīgošanu un reāllaika komunikāciju.

## Pārskatu un analītikas:

- Ģenerē visaptverošus pārskatus un analītiku par dažādiem uzņēmuma aspektiem.
- Nodrošina pielāgojamus vadības paneļus, lai uzraudzītu galvenos veiktspējas rādītājus (KPI).
- Ļauj lietotājiem definēt pielāgotus pārskatus, pamatojoties uz konkrētiem prasībām.
- Ievieš datu vizualizācijas tehnikas, lai vizuāli pievilcīgi attēlotu informāciju.

## Integrācija un pielāgošana:

- Nodrošina integrāciju ar populārām trešās puses lietojumprogrammām vai API, piemēram, e-pasta mārketinga rīkiem vai CRM platformām.
- Ļauj pielāgot lietotnes funkcionalitāti un izskatu, pamatojoties uz konkrētiem uzņēmuma vajadzībām.
- Nodrošina API vai tīmekļa āķus, lai atvieglotu datu apmaiņu starp ERP un CRM lietotni un citām sistēmām.

## Lietotājam draudzīga saskarne:

- Izstrādā intuitīvu, reaģējošu un lietotājam draudzīgu saskarni, izmantojot React.js un Ant Design.
- Ievieš viegli lietojamus navigācijas izvēlnes, meklēšanas funkcionalitāti un filtrus.
- Nodrošina vienotu un vizuāli pievilcīgu lietotāja saskarni dažādos ierīcēs un ekrāna izmēros.