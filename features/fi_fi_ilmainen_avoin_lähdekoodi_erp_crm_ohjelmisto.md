# IDURAR Avoin lähdekoodin ERP- ja CRM-ohje<PERSON>isto

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Verkkosivusto: [https://www.idurarapp.com](https://www.idurarapp.com)

## Ohjelmistopino

IDURAR on ilmainen avoimen lähdekoodin ERP- ja CRM-sovellus, joka perustuu "mern-stackiin": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Käyttäjähallinta:

- <PERSON><PERSON> ylläpitäjien luoda, muokata ja poistaa käyttäjätilit.
- To<PERSON><PERSON> roolipohjainen pääsynhallinta käyttäjäoikeuksien hallintaan.
- Tar<PERSON>a todennus- ja valtuutusmekanismit varmistaaksesi turvallisen pääsyn.

## Asiakassuhteen hallinta (CRM):

- Mahdollista käyttäjien luoda ja hallita yhteystietoja liideille, potentiaalisille asiakkaille ja asiakkaille.
- Toteuta liidien luonti- ja kelpoisuustoiminnot potentiaalisten myyntimahdollisuuksien seuraamiseksi.
- Tarjoa työkalut asiakaskommunikaation historian hallintaan, mukaan lukien sähköpostit, puhelut ja kokoukset.
- Salli käyttäjien aikatauluttaa tapaamisia ja lähettää ilmoituksia tai muistutuksia asiakkaille.

## Myynnin hallinta:

- Salli käyttäjien luoda ja hallita myyntitilauksia, liittämällä ne tiettyihin asiakkaisiin.
- Toteuta varaston seuranta tarkistaaksesi tuotteiden saatavuuden ja päivitä varastotasot jokaisen myynnin jälkeen.
- Luo laskuja ja käsittele maksujen integrointi suosittujen maksuyhdyskäytävien kanssa.
- Tarjoa kojetauluja ja raportteja myyntisuorituksen seuraamiseksi ja trendien analysoimiseksi.

## Hankinnan hallinta:

- Salli käyttäjien luoda ja hallita ostotilauksia, määrittämällä määrä ja halutut tuotteet.
- Seuraa toimittajatietoja ja hallitse toimittajasuhteita.
- Vastaanota tavaroita ja päivitä varastotasot sen mukaisesti.
- Käsittele ostolaskuja ja maksuja toimittajille.

## Varastonhallinta:

- Tarjoa työkaluja varastonhallinnan ja varastotason seurantaan, mukaan lukien varaston siirrot ja säädöt.
- Aseta automaattiset ilmoitukset matalista varastotasoista ja luo ostotilauksia, kun täydennystä tarvitaan.
- Tarjoa viivakoodinlukutaito tehokasta varastonhallintaa varten.
- Mahdollista käyttäjien luokitella tuotteet, määritellä ominaisuuksia ja asettaa hinnoittelutiedot.

## Taloushallinta:

- Toteuta pääkirjan järjestelmä taloudellisten tapahtumien seurantaan, mukaan lukien kulut ja tulot.
- Hallitse saamisia ja velkoja, mukaan lukien laskutus ja maksuseuranta.
- Luo taloudellisia raportteja, mukaan lukien tase- ja tuloslaskelmat.
- Integroi suosittujen kirjanpito-ohjelmistojen kanssa saumaton taloushallinta.

## Projektinhallinta:

- Tarjoa projektinhallintatoiminnot, joiden avulla käyttäjät voivat luoda ja seurata projekteja.
- Aseta tehtäviä tiimin jäsenille, aseta määräajat ja seuraa edistymistä.
- Allokoi resursseja ja seuraa projektikuluja.
- Tarjoa yhteistyöominaisuuksia, kuten asiakirjojen jakaminen ja reaaliaikainen viestintä.

## Raportointi ja analytiikka:

- Luo kattavia raportteja ja analytiikkaa yrityksen eri osa-alueista.
- Tarjoa muokattavia kojetauluja keskeisten suorituskykymittareiden (KPI) seurantaan.
- Salli käyttäjien määritellä mukautettuja raportteja tiettyjen vaatimusten perusteella.
- Toteuta tietojen visualisointitekniikoita esittääksesi tiedot visuaalisesti houkuttelevalla tavalla.

## Integraatio ja mukauttaminen:

- Mahdollista integraatio suosittujen kolmannen osapuolen sovellusten tai rajapintojen kanssa, kuten sähköpostimarkkinointityökalujen tai CRM-alustojen kanssa.
- Salli sovelluksen toiminnallisuuden ja ulkoasun mukauttaminen tiettyjen liiketoimintatarpeiden perusteella.
- Tarjoa API tai webhooks helpottamaan tietojen vaihtoa ERP- ja CRM-sovelluksen ja muiden järjestelmien välillä.

## Käyttäjäystävällinen käyttöliittymä:

- Suunnittele intuitiivinen, responsiivinen ja käyttäjäystävällinen käyttöliittymä käyttäen React.js:ää ja Ant Designia.
- Toteuta helppokäyttöiset navigointivalikot, hakuominaisuudet ja suodattimet.
- Varmista johdonmukainen ja visuaalisesti houkutteleva käyttöliittymä eri laitteissa ja näyttöjen koossa.