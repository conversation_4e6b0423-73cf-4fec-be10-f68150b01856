# IDURAR Open-Source ERP & CRM Softver

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Web stranica: [https://www.idurarapp.com](https://www.idurarapp.com)

## Tehnološki skup

IDURAR Besplatna open-source erp & crm aplikacija, bazirana na "mern-stack" : Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Upravljanje korisnicima:

- Omogućavanje administratorima stvaranje, uređivanje i brisanje korisničkih računa.
- Implementacija pristupa temeljenog na ulogama za upravljanje korisničkim dozvolama.
- Pružanje mehanizama za autentifikaciju i autorizaciju radi osiguravanja sigurnog pristupa.

## Upravljanje odnosima s klijentima (CRM):

- Omogućavanje korisnicima stvaranje i upravljanje kontaktima za potencijalne klijente i postojeće klijente.
- Implementacija funkcionalnosti generiranja i kvalifikacije potencijalnih prodajnih prilika radi praćenja mogućnosti prodaje.
- Pružanje alata za upravljanje povijesti komunikacije s klijentima, uključujući e-poštu, pozive i sastanke.
- Omogućavanje korisnicima zakazivanje sastanaka i slanje obavijesti ili podsjetnika klijentima.

## Upravljanje prodajom:

- Omogućavanje korisnicima stvaranje i upravljanje prodajnim narudžbama, povezujući ih s određenim klijentima.
- Implementacija praćenja inventara radi provjere dostupnosti proizvoda i ažuriranja stanja zaliha nakon svake prodaje.
- Generiranje računa i integracija s popularnim platnim uslugama.
- Pružanje nadzora i izvještaja za praćenje prodajne uspješnosti i analizu trendova.

## Upravljanje nabavkom:

- Omogućavanje korisnicima stvaranje i upravljanje narudžbama nabave, specificirajući količinu i željene proizvode.
- Praćenje informacija o dobavljačima i upravljanje odnosima s dobavljačima.
- Primanje robe i ažuriranje stanja zaliha prema tome.
- Upravljanje računima nabave i plaćanjima dobavljačima.

## Upravljanje zalihama:

- Pružanje alata za upravljanje i praćenje stanja zaliha, uključujući prijenose i prilagodbe zaliha.
- Postavljanje automatskih obavijesti za niske razine zaliha i generiranje narudžbi nabave kada je potrebno obnoviti zalihe.
- Omogućavanje mogućnosti skeniranja bar kodova radi učinkovitog upravljanja zalihama.
- Omogućavanje korisnicima kategorizaciju proizvoda, definiranje atributa i postavljanje informacija o cijenama.

## Financijsko upravljanje:

- Implementacija sustava glavne knjige za praćenje financijskih transakcija, uključujući troškove i prihode.
- Upravljanje potraživanjima i obvezama, uključujući izdavanje računa i praćenje plaćanja.
- Generiranje financijskih izvještaja, uključujući bilance i izvještaje o dobiti i gubitku.
- Integracija s popularnim računovodstvenim softverom radi besprijekornog financijskog upravljanja.

## Upravljanje projektima:

- Pružanje mogućnosti upravljanja projektima, omogućavajući korisnicima stvaranje i praćenje projekata.
- Dodjeljivanje zadataka članovima tima, postavljanje rokova i praćenje napretka.
- Alokacija resursa i praćenje troškova projekta.
- Pružanje značajki suradnje poput dijeljenja dokumenata i komunikacije u stvarnom vremenu.

## Izvješća i analitika:

- Generiranje sveobuhvatnih izvješća i analitike o različitim aspektima poslovanja.
- Pružanje prilagodljivih nadzornih ploča za praćenje ključnih pokazatelja uspješnosti (KPI).
- Omogućavanje korisnicima definiranje prilagođenih izvješća prema specifičnim zahtjevima.
- Implementacija tehnika vizualizacije podataka za prezentaciju informacija na vizualno privlačan način.

## Integracija i prilagođavanje:

- Omogućavanje integracije s popularnim aplikacijama ili API-ima trećih strana, poput alata za e-poštu ili platformi za upravljanje odnosima s klijentima.
- Omogućavanje prilagođavanja funkcionalnosti i izgleda aplikacije prema specifičnim poslovnim potrebama.
- Pružanje API-ja ili web-kuka za olakšavanje razmjene podataka između ERP & CRM aplikacije i drugih sustava.

## Korisničko sučelje jednostavno za korištenje:

- Dizajniranje intuitivnog, responsivnog i korisnički prijateljskog sučelja koristeći React.js i Ant Design.
- Implementacija jednostavnih navigacijskih izbornika, funkcionalnosti pretraživanja i filtara.
- Osiguravanje dosljednog i vizualno privlačnog korisničkog sučelja na različitim uređajima i veličinama zaslona.