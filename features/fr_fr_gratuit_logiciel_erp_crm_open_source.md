# IDURAR Logiciel ERP & CRM Open-Source

GitHub : [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Démo : [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Site Web : [https://www.idurarapp.com](https://www.idurarapp.com)

## Stack logiciel

IDURAR est une application ERP & CRM open-source gratuite, basée sur la pile "mern-stack" : Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Gestion des utilisateurs :

- Permettre aux administrateurs de créer, éditer et supprimer des comptes utilisateur.
- Mettre en place un contrôle d'accès basé sur les rôles pour gérer les autorisations des utilisateurs.
- Fournir des mécanismes d'authentification et d'autorisation pour garantir un accès sécurisé.

## Gestion de la relation client (CRM) :

- Permettre aux utilisateurs de créer et de gérer des fiches de contact pour les prospects et les clients.
- Mettre en place des fonctionnalités de génération et de qualification des leads pour suivre les opportunités de vente potentielles.
- Fournir des outils pour gérer l'historique des communications avec les clients, y compris les e-mails, les appels et les réunions.
- Permettre aux utilisateurs de planifier des rendez-vous et d'envoyer des notifications ou des rappels aux clients.

## Gestion des ventes :

- Permettre aux utilisateurs de créer et de gérer des commandes de vente, en les associant à des clients spécifiques.
- Mettre en place un suivi des stocks pour vérifier la disponibilité des produits et mettre à jour les niveaux de stock après chaque vente.
- Générer des factures et gérer l'intégration des paiements avec des passerelles de paiement populaires.
- Fournir des tableaux de bord et des rapports pour surveiller les performances des ventes et analyser les tendances.

## Gestion des achats :

- Permettre aux utilisateurs de créer et de gérer des commandes d'achat, en spécifiant la quantité et les produits souhaités.
- Suivre les informations sur les fournisseurs et gérer les relations avec les fournisseurs.
- Réceptionner les marchandises et mettre à jour les niveaux de stock en conséquence.
- Gérer les factures d'achat et les paiements aux fournisseurs.

## Gestion des stocks :

- Fournir des outils pour gérer et suivre les niveaux de stock, y compris les transferts de stock et les ajustements.
- Mettre en place des notifications automatiques pour les niveaux de stock bas et générer des commandes d'achat lorsque le réapprovisionnement est nécessaire.
- Offrir des capacités de numérisation de codes-barres pour une gestion efficace des stocks.
- Permettre aux utilisateurs de catégoriser les produits, de définir des attributs et des informations de tarification.

## Gestion financière :

- Mettre en place un système de grand livre pour suivre les transactions financières, y compris les dépenses et les revenus.
- Gérer les comptes clients et les comptes fournisseurs, y compris la facturation et le suivi des paiements.
- Générer des rapports financiers, y compris les bilans et les comptes de résultats.
- Intégrer des logiciels comptables populaires pour une gestion financière transparente.

## Gestion de projet :

- Fournir des fonctionnalités de gestion de projet, permettant aux utilisateurs de créer et de suivre des projets.
- Assigner des tâches aux membres de l'équipe, fixer des délais et suivre les progrès.
- Allouer des ressources et suivre les dépenses liées aux projets.
- Offrir des fonctionnalités de collaboration telles que le partage de documents et la communication en temps réel.

## Rapports et analyses :

- Générer des rapports complets et des analyses sur différents aspects de l'entreprise.
- Fournir des tableaux de bord personnalisables pour suivre les indicateurs de performance clés (KPI).
- Permettre aux utilisateurs de définir des rapports personnalisés en fonction de leurs besoins spécifiques.
- Mettre en place des techniques de visualisation des données pour présenter les informations de manière attrayante.

## Intégration et personnalisation :

- Permettre l'intégration avec des applications tierces populaires ou des API, telles que des outils de marketing par e-mail ou des plateformes CRM.
- Permettre la personnalisation de la fonctionnalité et de l'apparence de l'application en fonction des besoins spécifiques de l'entreprise.
- Fournir une API ou des webhooks pour faciliter l'échange de données entre l'application ERP & CRM et d'autres systèmes.

## Interface conviviale :

- Concevoir une interface intuitive, réactive et conviviale en utilisant React.js et Ant Design.
- Mettre en place des menus de navigation faciles à utiliser, des fonctionnalités de recherche et des filtres.
- Assurer une interface utilisateur cohérente et visuellement attrayante sur différents appareils et tailles d'écran.