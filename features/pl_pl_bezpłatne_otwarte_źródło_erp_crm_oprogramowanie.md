# Oprogramowanie IDURAR Open-Source ERP & CRM

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Strona internetowa: [https://www.idurarapp.com](https://www.idurarapp.com)

## Stos technologiczny

IDURAR Free to darmowe oprogramowanie ERP & CRM oparte na "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Zarządzanie użytkownikami:

- Pozwala administratorom tworzyć, edytować i usuwać konta użytkowników.
- Wdraża oparte na rolach zarządzanie dostępem do zarządzania uprawnieniami użytkowników.
- Zapewnia mechanizmy uwierzytelniania i autoryzacji w celu zapewnienia bezpiecznego dostępu.

## Zarządzanie relacjami z klientami (CRM):

- Umożliwia użytkownikom tworzenie i zarządzanie rekordami kontaktowymi dla potencjalnych klientów i klientów.
- Wdraża funkcje generowania i kwalifikowania potencjalnych leadów w celu śledzenia możliwości sprzedaży.
- Udostępnia narzędzia do zarządzania historią komunikacji z klientami, w tym e-mailem, rozmowami telefonicznymi i spotkaniami.
- Pozwala użytkownikom planować spotkania i wysyłać powiadomienia lub przypomnienia do klientów.

## Zarządzanie sprzedażą:

- Pozwala użytkownikom tworzyć i zarządzać zamówieniami sprzedaży, powiązując je z konkretnymi klientami.
- Wdraża śledzenie stanu magazynowego w celu sprawdzenia dostępności produktów i aktualizacji poziomów zapasów po każdej sprzedaży.
- Generuje faktury i obsługuje integrację płatności z popularnymi bramkami płatności.
- Udostępnia panele i raporty do monitorowania wyników sprzedaży i analizy trendów.

## Zarządzanie zakupami:

- Pozwala użytkownikom tworzyć i zarządzać zamówieniami zakupu, określając ilość i pożądane produkty.
- Śledzi informacje o dostawcach i zarządza relacjami z dostawcami.
- Odbiera towary i aktualizuje poziomy zapasów odpowiednio.
- Obsługuje faktury zakupowe i płatności dla dostawców.

## Zarządzanie magazynem:

- Udostępnia narzędzia do zarządzania i śledzenia poziomów zapasów, w tym transferów i dostosowań zapasów.
- Umożliwia ustawienie automatycznych powiadomień o niskim poziomie zapasów i generowanie zamówień zakupu w przypadku konieczności uzupełnienia zapasów.
- Oferuje możliwość skanowania kodów kreskowych w celu efektywnego zarządzania zapasami.
- Umożliwia użytkownikom kategoryzowanie produktów, definiowanie atrybutów i ustalanie informacji o cenach.

## Zarządzanie finansowe:

- Wdraża system księgowy do śledzenia transakcji finansowych, w tym wydatków i przychodów.
- Zarządza należnościami i zobowiązaniami, w tym fakturowaniem i śledzeniem płatności.
- Generuje raporty finansowe, w tym bilanse i rachunki zysków i strat.
- Integruje się z popularnym oprogramowaniem księgowym dla płynnego zarządzania finansami.

## Zarządzanie projektami:

- Oferuje możliwości zarządzania projektami, umożliwiając użytkownikom tworzenie i śledzenie projektów.
- Przydziela zadania członkom zespołu, ustala terminy i monitoruje postępy.
- Przydzielanie zasobów i śledzenie kosztów projektu.
- Oferuje funkcje współpracy, takie jak udostępnianie dokumentów i komunikacja w czasie rzeczywistym.

## Raportowanie i analiza:

- Generuje kompleksowe raporty i analizy dotyczące różnych aspektów działalności.
- Udostępnia spersonalizowane panele do monitorowania kluczowych wskaźników wydajności (KPI).
- Pozwala użytkownikom definiować niestandardowe raporty zgodnie z konkretnymi wymaganiami.
- Wdraża techniki wizualizacji danych w celu przedstawienia informacji w atrakcyjny sposób wizualny.

## Integracja i dostosowanie:

- Umożliwia integrację z popularnymi aplikacjami zewnętrznymi lub interfejsami API, takimi jak narzędzia do marketingu mailowego lub platformy CRM.
- Pozwala dostosować funkcjonalność i wygląd aplikacji zgodnie z konkretnymi potrzebami biznesowymi.
- Udostępnia interfejs API lub webhooki do ułatwienia wymiany danych między aplikacją ERP & CRM a innymi systemami.

## Przyjazny interfejs użytkownika:

- Projektuje intuicyjny, responsywny i przyjazny dla użytkownika interfejs przy użyciu React.js i Ant Design.
- Wdraża łatwo obsługiwane menu nawigacyjne, funkcje wyszukiwania i filtry.
- Zapewnia spójny i atrakcyjny wizualnie interfejs użytkownika na różnych urządzeniach i rozmiarach ekranu.