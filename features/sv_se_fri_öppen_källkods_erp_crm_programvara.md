# IDURAR Öppen källkod ERP & CRM-programvara

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Webbplats: [https://www.idurarapp.com](https://www.idurarapp.com)

## Programvarustack

IDURAR Gratis öppen källkod erp & crm-app, baserad på "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Användarhantering:

- Tillåt administratörer att skapa, redigera och ta bort användarkonton.
- Implementera rollbaserad åtkomstkontroll för att hantera användarbehörigheter.
- Tillhandahåll autentiserings- och auktoriseringsmekanismer för att säkerställa säker åtkomst.

## Kundrelationhantering (CRM):

- Möjliggör för användare att skapa och hantera kontaktregister för leads, prospekteringar och kunder.
- Implementera funktionalitet för leadgenerering och kvalificering för att följa potentiella försäljningsmöjligheter.
- Tillhandahåll verktyg för att hantera kundkommunikationshistorik, inklusive e-post, samtal och möten.
- Tillåt användare att schemalägga möten och skicka meddelanden eller påminnelser till kunder.

## Försäljningshantering:

- Tillåt användare att skapa och hantera försäljningsorder och koppla dem till specifika kunder.
- Implementera lagerövervakning för att kontrollera produkttillgänglighet och uppdatera lagerstatus efter varje försäljning.
- Generera fakturor och hantera betalningsintegration med populära betalningsportaler.
- Tillhandahåll instrumentpaneler och rapporter för att övervaka försäljningsprestanda och analysera trender.

## Inköpshantering:

- Tillåt användare att skapa och hantera inköpsorder och ange antal och önskade produkter.
- Spåra leverantörsinformation och hantera leverantörsrelationer.
- Ta emot varor och uppdatera lagerstatus därefter.
- Hantera inköpsfakturor och betalningar till leverantörer.

## Lagerhantering:

- Tillhandahåll verktyg för att hantera och spåra lagerstatus, inklusive lageröverföringar och justeringar.
- Ställ in automatiska aviseringar för låga lagerstatus och generera inköpsorder när påfyllning krävs.
- Erbjud streckkodsläsarfunktioner för effektiv lagerhantering.
- Möjliggör för användare att kategorisera produkter, definiera attribut och ange prisinformation.

## Ekonomihantering:

- Implementera ett huvudbokssystem för att spåra ekonomiska transaktioner, inklusive utgifter och intäkter.
- Hantera kundfordringar och leverantörsskulder, inklusive fakturering och betalningsuppföljning.
- Generera ekonomiska rapporter, inklusive balansräkningar och resultaträkningar.
- Integrera med populär redovisningsprogramvara för sömlös ekonomihantering.

## Projektledning:

- Tillhandahåll projektledningsfunktioner som gör att användare kan skapa och följa projekt.
- Tilldela uppgifter till teammedlemmar, ställ in deadlines och övervaka framsteg.
- Tilldela resurser och följa projektutgifter.
- Erbjud samarbetsfunktioner som dokumentdelning och kommunikation i realtid.

## Rapportering och analys:

- Generera omfattande rapporter och analyser om olika aspekter av verksamheten.
- Tillhandahåll anpassningsbara instrumentpaneler för att övervaka nyckeltal (KPI:er).
- Tillåt användare att definiera anpassade rapporter utifrån specifika krav.
- Implementera tekniker för datavisualisering för att presentera information på ett visuellt tilltalande sätt.

## Integration och anpassning:

- Möjliggör integration med populära tredjepartsapplikationer eller API:er, som e-postmarknadsföringsverktyg eller CRM-plattformar.
- Tillåt anpassning av appens funktionalitet och utseende baserat på specifika affärsbehov.
- Tillhandahåll en API eller webhooks för att underlätta datautbyte mellan ERP- och CRM-appen och andra system.

## Användarvänligt gränssnitt:

- Designa ett intuitivt, responsivt och användarvänligt gränssnitt med hjälp av React.js och Ant Design.
- Implementera lättanvända navigeringsmenyer, sökfunktioner och filter.
- Se till att gränssnittet är konsekvent och visuellt tilltalande på olika enheter och skärmstorlekar.