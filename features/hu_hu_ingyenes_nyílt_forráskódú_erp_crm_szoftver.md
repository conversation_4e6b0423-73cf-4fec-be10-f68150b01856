# IDURAR Nyílt forráskódú ERP & CRM szoftver

GitHub : [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo : [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Weboldal : [https://www.idurarapp.com](https://www.idurarapp.com)

## Szoftver technológia

Az IDURAR ingyenes nyílt forráskódú erp & crm alkalmazás, amely a "mern-stack" alapján működik: Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Felhasználókezelés:

- Lehetővé teszi az adminisztrátorok számára a felhasználói fiókok létrehozását, szerkesztését és törlését.
- Implementálja a szerep alapú hozzáférési vezérlést a felhasználói engedélyek kezeléséhez.
- Biztosít hitelesítési és jogosultságkezelési mechanizmusokat a biztonságos hozzáférés biztosításához.

## Ügyfélkapcsolat-kezelés (CRM):

- Lehetővé teszi a felhasználók számára a kapcsolatfelvételi adatok létrehozását és kezelését a potenciális vevők és ügyfelek számára.
- Implementálja a vezetékképzési és minősítési funkciókat a potenciális értékesítési lehetőségek nyomon követéséhez.
- Biztosít eszközöket az ügyfél kommunikációs előzményeinek kezeléséhez, beleértve az e-maileket, hívásokat és találkozókat.
- Lehetővé teszi a felhasználóknak az időpontok ütemezését és értesítéseket vagy emlékeztetőket küld az ügyfeleknek.

## Értékesítési kezelés:

- Lehetővé teszi a felhasználók számára az értékesítési rendelések létrehozását és kezelését, azokat az adott ügyfelekkel kapcsolatosan.
- Implementálja az áruk nyomon követését a termék elérhetőségének ellenőrzéséhez és a készletszintek frissítéséhez minden értékesítés után.
- Számlákat generál és kezeli a fizetési integrációt a népszerű fizetési átjárókkal.
- Biztosít irányítópultokat és jelentéseket az értékesítési teljesítmény monitorozásához és a trendek elemzéséhez.

## Beszerzési kezelés:

- Lehetővé teszi a felhasználók számára a beszerzési rendelések létrehozását és kezelését, a mennyiség és a kívánt termékek meghatározásával.
- Követi a beszállítói információkat és kezeli a beszállítói kapcsolatokat.
- Fogadja a termékeket és frissíti a készletszinteket ennek megfelelően.
- Kezeli a beszerzési számlákat és a beszállítókhoz történő fizetéseket.

## Készletkezelés:

- Biztosít eszközöket a készletszintek kezeléséhez és nyomon követéséhez, beleértve a készletátadásokat és a beállításokat.
- Beállít automatikus értesítéseket az alacsony készletszintekről és generáljon beszerzési rendeléseket, amikor a készlet újratöltésére van szükség.
- Kínál vonalkódolvasási képességeket hatékony készletkezeléshez.
- Lehetővé teszi a felhasználóknak a termékek kategorizálását, attribútumok meghatározását és árazási információk beállítását.

## Pénzügyi kezelés:

- Implementáljon egy általános könyvelő rendszert a pénzügyi tranzakciók nyomon követéséhez, beleértve a költségeket és a bevételeket.
- Kezelje az eladói és vevői számlákat, beleértve a számlázást és a fizetés nyomon követését.
- Generáljon pénzügyi jelentéseket, beleértve az egyenlegkimutatást és az eredménykimutatást.
- Integráljon népszerű könyvelő szoftverekkel a zökkenőmentes pénzügyi kezelés érdekében.

## Projektkezelés:

- Biztosítson projektmenedzsment képességeket, amelyek lehetővé teszik a felhasználók számára a projektek létrehozását és nyomon követését.
- Rendeljen feladatokat a csapattagoknak, állítson határidőket és kövesse nyomon a haladást.
- Allokáljon erőforrásokat és kövesse a projekt kiadásait.
- Kínáljon együttműködési funkciókat, például dokumentummegosztást és valós idejű kommunikációt.

## Jelentések és analitika:

- Generáljon átfogó jelentéseket és elemzéseket a vállalkozás különböző aspektusairól.
- Biztosítson testreszabható irányítópultokat a kulcsfontosságú teljesítménymutatók (KPI-k) monitorozásához.
- Lehetővé teszi a felhasználók számára a specifikus követelmények alapján meghatározott egyedi jelentések meghatározását.
- Implementálja az adatvizualizációs technikákat az információk vonzó módon történő bemutatásához.

## Integráció és testreszabás:

- Lehetővé teszi a népszerű harmadik fél alkalmazások vagy API-k, például az e-mail marketing eszközök vagy a CRM platformok integrálását.
- Lehetővé teszi az alkalmazás funkcionalitásának és megjelenésének testreszabását a konkrét üzleti igények alapján.
- Biztosítson API-t vagy webhorgokat az adatcserének megkönnyítéséhez az ERP & CRM alkalmazás és más rendszerek között.

## Felhasználóbarát felület:

- Készítsen intuitív, reszponzív és felhasználóbarát felületet React.js és Ant Design segítségével.
- Implementáljon könnyen használható navigációs menük, keresési funkciókat és szűrőket.
- Biztosítson következetes és vizuálisan vonzó felhasználói felületet különböző eszközökön és képernyőméreteken.