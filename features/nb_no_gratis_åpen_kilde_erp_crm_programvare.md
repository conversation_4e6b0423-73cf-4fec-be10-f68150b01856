# IDURAR Åpen kildekode ERP og CRM-programvare

GitHub: [https://github.com/idurar/idurar-erp-crm](https://github.com/idurar/idurar-erp-crm)
Demo: [https://www.idurarapp.com/open-source-erp-crm/](https://www.idurarapp.com/open-source-erp-crm/)
Nettsted: [https://www.idurarapp.com](https://www.idurarapp.com)

## Programvarestabel

IDURAR Gratis åpen kildekode ERP og CRM-app, basert på "mern-stack": Node.js React.js Redux Express.js MongoDB AntDesign (AntD)

## Brukerstyring:

- Tillat administratorer å opprette, redigere og slette brukerkontoer.
- Implementer rollebasert tilgangskontroll for å administrere brukerrettigheter.
- Tilby autentiserings- og autorisasjonsmekanismer for å sikre sikker tilgang.

## Kunderelasjonshåndtering (CRM):

- <PERSON><PERSON><PERSON><PERSON> det mulig for brukere å opprette og administrere kontaktregistre for potensielle kunder og kunder.
- Implementer funksjonalitet for generering og kvalifisering av potensielle salgsmuligheter.
- Tilby verktøy for å administrere kommunikasjonshistorikk med kunder, inkludert e-poster, samtaler og møter.
- Tillat brukere å planlegge avtaler og sende varsler eller påminnelser til kunder.

## Salgsstyring:

- Tillat brukere å opprette og administrere salgsordrer og knytte dem til spesifikke kunder.
- Implementer lagerstyring for å sjekke produkttilgjengelighet og oppdatere beholdningsnivåer etter hver salg.
- Generer fakturaer og håndter betalingsintegrasjon med populære betalingsportaler.
- Tilby dashboards og rapporter for å overvåke salgsprestasjoner og analysere trender.

## Innkjøpsstyring:

- Tillat brukere å opprette og administrere innkjøpsordrer, spesifisere mengde og ønskede produkter.
- Spor leverandørinformasjon og administrer leverandørforhold.
- Motta varer og oppdater beholdningsnivåer i henhold.
- Håndter innkjøpsfakturaer og betalinger til leverandører.

## Lagerstyring:

- Tilby verktøy for å administrere og spore beholdningsnivåer, inkludert vareoverføringer og justeringer.
- Sett opp automatiske varsler for lav beholdning og generer innkjøpsordrer når det er behov for påfylling.
- Tilby strekkodeskanning for effektiv lagerstyring.
- Gjør det mulig for brukere å kategorisere produkter, definere attributter og angi priser.

## Økonomistyring:

- Implementer et hovedboksystem for å spore økonomiske transaksjoner, inkludert utgifter og inntekter.
- Administrer utestående fordringer og gjeld, inkludert fakturering og betalingssporing.
- Generer økonomiske rapporter, inkludert balanse og resultatregnskap.
- Integrer med populær regnskapsprogramvare for sømløs økonomistyring.

## Prosjektstyring:

- Tilby prosjektstyringsfunksjoner som lar brukere opprette og følge opp prosjekter.
- Tildel oppgaver til teammedlemmer, sett frister og overvåk fremdrift.
- Alloker ressurser og spore prosjektkostnader.
- Tilby samarbeidsfunksjoner som dokumentdeling og sanntidskommunikasjon.

## Rapportering og analyse:

- Generer omfattende rapporter og analyser om ulike aspekter av virksomheten.
- Tilby tilpassbare dashboards for overvåking av nøkkelindikatorer for ytelse (KPIer).
- Tillat brukere å definere egendefinerte rapporter basert på spesifikke krav.
- Implementer datavisualiseringsteknikker for å presentere informasjon på en visuelt tiltalende måte.

## Integrering og tilpasning:

- Gjør det mulig med integrasjon med populære tredjepartsapplikasjoner eller API-er, som e-postmarkedsføringsverktøy eller CRM-plattformer.
- Tillat tilpasning av appens funksjonalitet og utseende basert på spesifikke forretningsbehov.
- Tilby en API eller webhooks for å lette datautveksling mellom ERP- og CRM-appen og andre systemer.

## Brukervennlig grensesnitt:

- Design et intuitivt, responsivt og brukervennlig grensesnitt ved hjelp av React.js og Ant Design.
- Implementer enkelt navigasjonsmenyer, søkefunksjoner og filtre.
- Sørg for en konsistent og visuelt tiltalende brukergrensesnitt på ulike enheter og skjermstørrelser.